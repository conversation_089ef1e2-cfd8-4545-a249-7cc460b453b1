<template>
  <ul>
    <li v-for="(item, index) in srcList" :key="index">
      <el-image
        style="width: 40px; height: 40px"
        :src="item"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="srcList"
        show-progress
        :initial-index="index"
        fit="cover"
      />
    </li>
  </ul>
</template>
<script>
export default {
    props:{
        imgList: String
    },
    computed:{
        srcList(){
            if(this.imgList) return this.imgList.split(',')
            else return []
        }
    }
}
</script>
<style lang="scss" scoped>
ul{
    display:flex;
    flex-wrap:wrap;
    flex-shrink: 0;
    gap:10px;
}
</style>
