<template>
  <div class="product-add" v-loading="pageLoading">
    <!--form表单-->
    <el-form size="small" ref="form" :model="formData" label-width="auto">
      <div class="common-form">企业信息</div>
      <el-form-item label="id" prop="id" >
        <el-input v-model="formData.id" placeholder="请输入id" class="max-w460" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="地市" prop="city" >
        <el-input v-model="formData.city" placeholder="请输入地市" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName" >
        <el-input v-model="formData.customerName" placeholder="请输入客户名称" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="区县" prop="area" >
        <el-input v-model="formData.area" placeholder="请输入区县" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="支局" prop="branchOffice" >
        <el-input v-model="formData.branchOffice" placeholder="请输入支局" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="支局id" prop="branchId" >
        <el-input v-model="formData.branchId" placeholder="请输入支局id" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="客户经理" prop="accountManager" >
        <el-input v-model="formData.accountManager" placeholder="请输入客户经理" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contactNumber" >
        <el-input v-model="formData.contactNumber" placeholder="请输入联系电话" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="社会统一信用代码" prop="unifiedSocialCreditCode">
        <el-input v-model="formData.unifiedSocialCreditCode" placeholder="请输入社会统一信用代码" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="客户属性" prop="customerAttribute" >
        <el-input v-model="formData.customerAttribute" placeholder="请输入客户属性" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="锁定情况" prop="lockStatus" >
        <el-input v-model="formData.lockStatus" placeholder="请输入锁定情况" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="地址" prop="address" >
        <el-input v-model="formData.address" placeholder="请输入地址" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="集团P码" prop="groupPcode" >
        <el-input v-model="formData.groupPcode" placeholder="请输入集团P码" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="存增类型" prop="increaseType" >
        <el-input v-model="formData.increaseType" placeholder="请输入存增类型" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="金额" prop="amount" >
        <el-input v-model="formData.amount" placeholder="请输入金额" class="max-w460"></el-input>
      </el-form-item>
      <el-form-item label="企业状态" prop="enterpriseStatus" >
        <el-input v-model="formData.enterpriseStatus" placeholder="请输入企业状态" class="max-w460"></el-input>
      </el-form-item>
    </el-form>

    <div class="common-button-wrapper">
      <el-button size="small" type="info" @click="cancelFunc">取消</el-button>
      <el-button size="small" type="primary" @click="onSubmit">提交</el-button>
    </div>
  </div>
</template>

<script>
import EnterpriseManagementApi from "@/api/enterpriseManagement.js";
import {ElMessage} from "element-plus";
export default {
  components: {
  },
  data() {
    return {
      // 页面是否正在加载
      pageLoading: true,
      // 表单数据
      formData: {
        id: null,
        city: '',
        customerName: '',
        area: '',
        branchOffice: '',
        branchId: null,
        accountManager: '',
        contactNumber: '',
        unifiedSocialCreditCode: '',
        customerAttribute: '',
        lockStatus: '',
        address: '',
        groupPcode: '',
        increaseType: '',
        amount: null,
        enterpriseStatus: '',
      },
    };
  },
  created() {
    //获取列表
    this.getParams();
  },
  methods: {
    /*获取参数*/
    getParams() {
      let self = this;
      self.pageLoading = true;
      // 取到路由带过来的参数
      const params = this.$route.query.id;
      // 用id取企业详情
      EnterpriseManagementApi.getDetail({id: params},true).then(res=>{
        // 填充
        this.formData = res.data;
        self.pageLoading = false;
      });
    },
    // 提交
    onSubmit() {
      let params = this.formData;
      EnterpriseManagementApi.update(params, true).then(res=>{
        if (res.errCode === 200){
          ElMessage({
            message: "修改成功",
            type: "success",
          });
          // 刷新页面
          this.getParams();
        } else{
          ElMessage({
            message: "修改失败",
            type: "error",
          });
        }
      });
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
  .product-add {
    padding-bottom: 50px;
  }
</style>
