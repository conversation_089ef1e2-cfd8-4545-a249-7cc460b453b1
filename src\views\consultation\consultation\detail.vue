<!--查看咨询单详情-->
<template>
  <div class="pb50" v-loading="loading">
    <!--内容-->
    <div class="product-content">
      <!--基本信息-->
      <div class="common-form">基本信息</div>
      <div class="table-wrap">
        <el-row>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">咨询单编号：</span>
              {{ consultation.consultationId }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">创建时间：</span>
              {{ consultation.createTime }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">发起人账号：</span>
              {{ consultation.supplierUserName }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">发起人姓名：</span>
              {{ consultation.supplierUserRealName }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">当前环节类型：</span>
              <span>{{translateProcessType(consultation.currentProcessType)}}</span>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">完成时间：</span>
              {{consultation.finishTime}}
            </div>
          </el-col>
        </el-row>
      </div>
      <!--展示咨询单记录-->
      <div>
        <div class="common-form mt16">咨询单记录</div>
        <div class="table-wrap">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in consultationRecordList" :key="index" :timestamp="item.createTime"
                              :hide-timestamp="true" :color="getTimelineColor(item.finishStatus)">
              <el-row>
                <!--<el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">咨询单记录编码:</span>
                    {{item.consultationRecordId}}
                  </div>
                </el-col>-->
                <el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">用户姓名:</span>
                    {{item.supplierUserRealName}}
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">流程类型:</span>
                    <span>{{translateProcessType(item.processType)}}</span>
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">状态:</span>
                    <span v-if="item.finishStatus === 0">处理中</span>
                    <span v-if="item.finishStatus === 1">已完成</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="pb16">
                    <span class="gray9">意见或建议:</span>
                    {{item.opinion}}
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="pb16">
                    <span class="gray9">创建时间:</span>
                    {{item.createTime}}
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="pb16">
                    <span class="gray9">完成时间:</span>
                    {{item.finishTime}}
                  </div>
                </el-col>
              </el-row>
            </el-timeline-item>
          </el-timeline>
<!--          <el-button type="primary" @click="completeImplementation">
            完成实施
          </el-button>-->
        </div>
      </div>
    </div>
    <div class="common-button-wrapper">
      <el-button size="small" type="info" @click="cancelFunc">返回上一页</el-button>
    </div>
  </div>
</template>

<script>
import {ElMessage} from "element-plus";
import ConsultationApi from "@/api/consultation.js";
export default {
  components: {
  },
  data() {
    return {
      /*是否加载完成*/
      loading: true,
      // 咨询单基础信息
      consultation:{
        consultationId:null,
        createTime:'',
        updateTime:'',
        supplierUserId:null,
        currentProcessType:null,
        currentProcessRecordId:null,
        finishTime:'',
        supplierUserName:'',
        supplierUserRealName:'',
      },
      // 咨询单详细记录
      consultationRecordList:[],
      /*是否打开添加弹窗*/
      open_add: false,
      /*一页多少条*/
      pageSize: 20,
      /*一共多少条数据*/
      totalDataNumber: 0,
      /*当前是第几页*/
      curPage: 1,
    };
  },
  created() {
    this.getParams();
  },
  methods: {
    //获取参数，刷新页面
    getParams() {
      // 取到路由带过来的参数
      const params = this.$route.query.consultationId;
      ConsultationApi.getConsultationById({'consultationId':params}, true).then(res=>{
        console.log(res);
        this.consultation = res.data;
        this.consultationRecordList = res.data.consultationRecordList;
        this.loading = false;
      }).catch(error=>{
        this.loading = false;
      });
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
    // 取timeline的颜色
    getTimelineColor(status){
      if (status === 0){
        // 处理中
        return '#7d7d7d';
      }
      if (status === 1){
        // 已完成
        return '#0bbd87';
      }
      return null;
    },
    // 完成实施
    completeImplementation(){
      // 取到路由带过来的参数
      /*const params = this.$route.query.orderId;
      OrderImplementationRecordApi.completeImplementation({orderId: params}).then(res=>{
        ElMessage({
          message: "实施成功",
          type: "success",
        });
        // 刷新数据
        this.getParams();
      });*/
    },
    // 翻译流程类型
    translateProcessType(processType){
      if (processType === 1){
        return "编辑问题";
      }
      if (processType === 2){
        return "专家处理";
      }
      if (processType === 3){
        return "竣工";
      }
      return processType;
    }
  }
};
</script>
<style></style>
