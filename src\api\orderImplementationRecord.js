import request from '@/utils/request'
let OrderImplementationRecordApi = {
    /*查询*/
    getlist(data, errorback) {
        return request._postBody('/supplier/order/orderImplementationRecord/list', data, errorback);
    },
    /*查询，不分页*/
    getListNotPaged(data, errorback) {
        return request._postBody('/supplier/order/orderImplementationRecord/listNotPaged', data, errorback);
    },
    // 根据订单信息查询其供应商可选的施工员信息列表，不分页
    getImplementerListNotPaged(data, errorback) {
        return request._postBody('/supplier/order/orderImplementationRecord/getImplementerListNotPaged', data, errorback);
    },
    /*订单列表*/
    orderlist(data, errorback) {
        return request._postBody('/supplier/order/orderImplementationRecord/index', data, errorback);
    },
    // 商户端-施工人员完成实施的接口
    completeImplementation(data, errorback) {
        return request._postBody('/supplier/order/orderImplementationRecord/completeImplementation', data, errorback);
    },
}

export default OrderImplementationRecordApi;
