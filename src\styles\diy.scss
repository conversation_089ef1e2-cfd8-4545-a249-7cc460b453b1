.diy-container {
    display: flex;
    .diy-menu {
      padding: 10px;
      width: 120px;
      box-sizing: border-box;
      border: 1px solid #dddddd;
      height: calc(100vh - 150px);
      overflow-y: auto;
    }
    .min-group {
      .hd {
        position: relative;
        height: 30px;
        line-height: 30px;
        color: #CCCCCC;
        font-size: 14px;
        &::after{
          position: absolute;
          content: '';
          border: 4px solid transparent;
          border-top-color: #CCCCCC;
          right: 0;
          top: 12px;
        }
      }
      .bd{
        .item{
          position: relative;
          width: 100%;
          height: 30px;
          line-height: 30px;
          margin-bottom: 4px;
          border-top: 1px solid #CCCCCC;
          cursor: pointer;
          &:hover{
            color: #3a8ee6;
          }
          &:hover::after{
            position: absolute;
            top: 7px;
            right: 0;
            display: block;
            content: '+';
            width: 16px;
            height: 16px;
            line-height: 16px;
            border-radius: 50%;
            text-align: center;
            border: 1px solid #3a8ee6;
          }
        }
      }
    }
    .p-icon {
      display: none;
      padding-top: 6px;
    }
    .p-text {
      line-height: 20px;
    }
    .diy-phone {
      width: 375px;
      margin: 0 30px;
      border: 1px solid #CCCCCC;
      border-radius: 18px;
      overflow: hidden;
    }
    .diy-info {
      flex: 1;
      padding: 10px;
      border: 1px solid #CCCCCC;
      height: calc(100vh - 150px);
      overflow-y: auto;
    }
}
