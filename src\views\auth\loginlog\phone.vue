<template>
  <!--手机端、大屏登录日志-->
  <div class="user">
    <!--搜索表单-->
    <div class="common-seach-wrap">
      <el-form
        size="small"
        :inline="true"
        :model="searchForm"
        class="demo-form-inline"
      >
        <el-form-item>
          <el-input
            size="small"
            v-model="searchForm.area"
            placeholder="请输入区县"
            clearable
            style="width: 100px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            size="small"
            v-model="searchForm.username"
            placeholder="请输入用户登录名"
            clearable
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            size="small"
            v-model="searchForm.realName"
            placeholder="请输入用户姓名"
            clearable
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="起始时间">
          <div class="block">
            <span class="demonstration"></span>
            <el-date-picker size="small" v-model="searchForm.createTime" type="daterange" value-format="YYYY-MM-DD"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            size="small"
            type="primary"
            icon="Search"
            @click="searchSubmit"
            >查询
          </el-button>
          <el-button
            size="small"
            @click="resetSearch"
          >重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-tabs v-model="activeTabName" @tab-change="handleSwitchTab">
      <el-tab-pane label="详细记录" name="tab-detail">
        <div class="table-wrap">
          <el-table
            size="small"
            :data="tableDataDetail"
            border
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column prop="loginId" label="ID"></el-table-column>
            <el-table-column prop="area" label="所属区县"></el-table-column>
            <el-table-column prop="username" label="用户登录名"></el-table-column>
            <el-table-column prop="realName" label="用户姓名"></el-table-column>
            <!--<el-table-column prop="content" label="登录操作"></el-table-column>-->
            <el-table-column prop="createTime" label="登录时间"></el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="统计情况" name="tab-count">
        <div class="table-wrap">
          <el-table
            size="small"
            :data="tableDataCount"
            border
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column prop="area" label="所属区县"></el-table-column>
            <el-table-column prop="username" label="用户登录名"></el-table-column>
            <el-table-column prop="realName" label="用户姓名"></el-table-column>
            <el-table-column prop="loginCount" label="登录次数"></el-table-column>
            <el-table-column prop="lastLoginTime" label="最近登录时间"></el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!--内容-->
    <div class="product-content">
      <!--分页-->
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          :current-page="curPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalDataNumber"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import AuthApi from "@/api/auth.js";
import qs from "qs";
import {useUserStore} from "@/store";
const { token } = useUserStore();
export default {
  components: {},
  //inject: ["reload"],
  data() {
    return {
      token,
      /*是否加载完成*/
      loading: true,
      /*列表数据-详情*/
      tableDataDetail: [],
      /*列表数据-统计*/
      tableDataCount: [],
      /*一页多少条*/
      pageSize: 20,
      /*一共多少条数据*/
      totalDataNumber: 0,
      /*当前是第几页*/
      curPage: 1,
      /*搜索参数*/
      searchForm: {
        area:"",//区县
        username: "",//用户登录名
        realName:"",//用户姓名
        createTime:"",//时间
      },
      // 激活标签的名称
      activeTabName: "tab-detail",
    };
  },
  created() {
    /*获取列表*/
    this.getTableList();
  },
  methods: {
    /*搜索*/
    searchSubmit() {
      this.curPage = 1;
      this.getTableList();
    },
    // 重置搜索
    resetSearch(){
      this.searchForm.area = "";
      this.searchForm.username = "";
      this.searchForm.realName = "";
      this.searchForm.createTime = "";
      this.curPage = 1;
      this.getTableList();
    },
    /*选择第几页*/
    handleCurrentChange(val) {
      let self = this;
      self.curPage = val;
      self.loading = true;
      self.getTableList();
    },
    /*每页多少条*/
    handleSizeChange(val) {
      this.curPage = 1;
      this.pageSize = val;
      this.getTableList();
    },
    /*获取列表*/
    getTableList() {
      let self = this;
      let search = this.searchForm;
      // 构造参数
      let Params = {};
      Params.area = search.area;
      Params.username = search.username;
      Params.realName = search.realName;
      Params.pageIndex = self.curPage;
      Params.pageSize = self.pageSize;
      if(search.createTime && search.createTime.length>1){
        Params.startDate = search.createTime[0];
        Params.endDate = search.createTime[1];
      }
      // 根据激活的tab不同进行不同的查询
      if (this.activeTabName === 'tab-detail'){
        AuthApi.frontUserLoginlog(Params, true)
          .then((res) => {
            self.loading = false;
            self.tableDataDetail = res.data.records;
            self.totalDataNumber = res.data.total;
          })
          .catch((error) => {});
      }else if (this.activeTabName === 'tab-count'){
        AuthApi.frontUserLoginlogCount(Params, true)
          .then((res) => {
            self.loading = false;
            self.tableDataCount = res.data.records;
            self.totalDataNumber = res.data.total;
          })
          .catch((error) => {});
      }
    },
    /*切换菜单*/
    handleSwitchTab(tab) {
      console.log('handleSwitchTab');
      console.log(tab);
      // 切换菜单后搜索
      this.curPage = 1;
      this.getTableList();
      //let self = this;
      //self.curPage = 1;
      //self.searchForm.flowType = tab;
      //self.getData();
    },
    // 导出查询结果到excel文件
    onExport: function() {
      let self = this;
      let search = this.searchForm;
      let Params = {};
      if(search.createTime && search.createTime.length>1){
        Params.startDate = search.createTime[0];
        Params.endDate = search.createTime[1];
      }
      Params.area = search.area;
      Params.username = search.username;
      Params.realName = search.realName;
      Params.tokensupplier = self.token;
      let baseUrl = import.meta.env.VITE_BASIC_URL;
      if (this.activeTabName === 'tab-detail'){
        window.location.href = baseUrl + '/api/supplier/auth/loginlog/front/exportExcelForUserLoginLog?' + qs.stringify(Params);
      }else if (this.activeTabName === 'tab-count'){
        window.location.href = baseUrl + '/api/supplier/auth/loginlog/front/exportExcelForUserLoginLogCountVo?' + qs.stringify(Params);
      }else {
        console.log('导出失败，原因为activeTabName错误')
      }
    },
  },
};
</script>

<style></style>
