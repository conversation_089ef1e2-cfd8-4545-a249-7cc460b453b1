<!--查看咨询单详情-->
<template>
  <div class="feedback-detail-container" v-loading="loading">
    <!--内容-->
    <div class="detail-content">
      <!--基本信息-->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">
            <div class="title-icon">
              <el-icon>
                <Document />
              </el-icon>
            </div>
            <span class="title-text">基本信息</span>
            <div class="title-decoration"></div>
          </div>
        </div>
        <div class="info-card">
          <div class="card-background"></div>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item customer-info">
                <div class="info-icon">
                  <el-icon>
                    <User />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">客户名称</div>
                  <div class="info-value">{{ consultation.customerName || '--' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item time-info">
                <div class="info-icon">
                  <el-icon>
                    <Calendar />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">创建时间</div>
                  <div class="info-value">{{ consultation.createTime || '--' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item contact-info">
                <div class="info-icon">
                  <el-icon>
                    <UserFilled />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">联系人姓名</div>
                  <div class="info-value">{{ consultation.contactName || '--' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item phone-info">
                <div class="info-icon">
                  <el-icon>
                    <Iphone />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">联系方式</div>
                  <div class="info-value">{{ consultation.contactPhone || '--' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item status-info">
                <div class="info-icon">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">当前状态</div>
                  <div class="info-value">
                    <el-tag :type="getStatusType(consultation.status)"
                      :effect="consultation.status === 6 ? 'dark' : 'light'" size="default" class="status-tag">
                      {{ getStatusText(consultation.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item finish-info">
                <div class="info-icon">
                  <el-icon>
                    <Finished />
                  </el-icon>
                </div>
                <div class="info-content">
                  <div class="info-label">完成时间</div>
                  <div class="info-value">{{ consultation.feedbackTime || '--' }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <!--展示任务描述以及附件-->
      <div class="description-section">
        <div class="description-card">
          <div class="card-background"></div>

          <!-- 任务描述 -->
          <div class="description-content">
            <div class="content-header">
              <div class="content-icon">
                <el-icon><Edit /></el-icon>
              </div>
              <h4 class="content-title">任务描述</h4>
            </div>
            <div class="description-text">
              {{ consultation.description || '暂无描述' }}
            </div>
          </div>

          <!-- 附件列表 -->
          <div class="attachment-content" v-if="consultation.attachmentList && consultation.attachmentList.length > 0">
            <div class="content-header">
              <div class="content-icon">
               <el-icon><Paperclip /></el-icon>
              </div>
              <h4 class="content-title">相关附件</h4>
              <el-tag size="small" type="info" effect="plain">{{ consultation.attachmentList.length }} 个文件</el-tag>
            </div>
            <div class="attachment-list">
              <div v-for="(file, index) in consultation.attachmentList" :key="index" class="attachment-item"
                @click="downloadFile(file.fileUrl, file.fileName)">
                <div class="file-icon">
                  <i :class="getFileIcon(file.fileName)"></i>
                  <el-icon><DArrowLeft /></el-icon>
                </div>
                <div class="file-info">
                  <div class="file-name">{{ file.fileName }}</div>
                  <div class="file-action">点击下载</div>
                </div>
                <div class="download-icon">
                  <i class="el-icon-download"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 无附件状态 -->
          <div class="no-attachment" v-else>
            <div class="content-header">
              <div class="content-icon">
                <i class="el-icon-paperclip"></i>
              </div>
              <h4 class="content-title">相关附件</h4>
            </div>
            <div class="empty-attachment">
              <i class="el-icon-document-delete"></i>
              <span>暂无附件</span>
            </div>
          </div>
        </div>
      </div>

      <div class="timeline-section">
        <!-- <div class="section-header">
          <div class="section-title">
            <div class="title-icon">
              <i class="el-icon-time"></i>
            </div>
            <span class="title-text">咨询单记录</span>
            <div class="title-decoration"></div>
          </div>
          <div class="timeline-stats" v-if="consultationRecordList.length > 0">
            <div class="stats-item">
              <span class="stats-number">{{ consultationRecordList.length }}</span>
              <span class="stats-label">总记录数</span>
            </div>
            <div class="stats-item">
              <span class="stats-number">{{ getCompletedCount() }}</span>
              <span class="stats-label">已完成</span>
            </div>
          </div>
        </div> -->
        <div class="timeline-card">
          <div class="timeline-background"></div>
          <el-timeline v-if="consultationRecordList.length > 0" class="custom-timeline">
            <el-timeline-item v-for="(item, index) in consultationRecordList" :key="index" :timestamp="item.createTime"
              :hide-timestamp="false" :color="getTimelineColor(item.status)" :size="index === 0 ? 'large' : 'normal'"
              :type="getTimelineType(item.status)">
              <div class="timeline-content">
                <!-- <div class="timeline-step">{{ index + 1 }}</div> -->
                <div class="timeline-header">
                  <div class="timeline-title-wrapper">
                    <h4 class="timeline-title">{{ item.taskName }}</h4>
                    <div class="timeline-subtitle">{{ item.createTime }}</div>
                  </div>
                  <el-tag :type="getRecordStatusType(item.result || item.status)" size="default" effect="light"
                    class="timeline-status-tag">
                    <i :class="getStatusIcon(item.result || item.status)"></i>
                    {{ item.result || item.status }}
                  </el-tag>
                </div>
                <div class="timeline-details">
                  <el-row :gutter="20">
                    <el-col :span="12" v-if="$route.query.status > 5">
                      <div class="detail-item">
                        <div class="detail-icon">
                          <el-icon>
                            <VideoPlay />
                          </el-icon>
                        </div>
                        <div class="detail-content">
                          <span class="detail-label">开始时间</span>
                          <span class="detail-value">{{ item.startTime || '--' }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12" v-if="$route.query.status > 5">
                      <div class="detail-item">
                        <div class="detail-icon">
                          <el-icon>
                            <VideoPause />
                          </el-icon>
                        </div>
                        <div class="detail-content">
                          <span class="detail-label">完成时间</span>
                          <span class="detail-value">{{ item.endTime || '--' }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12" v-else>
                      <div class="detail-item">
                        <div class="detail-icon">
                          <el-icon>
                            <Checked />
                          </el-icon>
                        </div>
                        <div class="detail-content">
                          <span class="detail-label">审核时间</span>
                          <span class="detail-value">{{ item.auditTime || '--' }}</span>
                        </div>
                      </div>
                    </el-col>
                  </el-row>

                  <!-- 专家处理备注和附件 -->
                  <div v-if="isExpertProcess(item.taskName) && (item.remark || item.remark1)" class="expert-section">
                    <div class="section-divider"></div>
                    <div class="expert-content">
                      <!-- 专家备注 -->
                      <div v-if="item.remark" class="expert-remark">
                        <div class="remark-header">
                          <div class="remark-icon">
                            <i class="el-icon-chat-line-square"></i>
                          </div>
                          <h5 class="remark-title">专家处理备注</h5>
                        </div>
                        <div class="remark-content">{{ item.remark }}</div>
                      </div>

                      <!-- 专家附件 -->
                      <div v-if="item.remark1" class="expert-attachments">
                        <div class="attachment-header">
                          <div class="attachment-icon">
                            <i class="el-icon-paperclip"></i>
                          </div>
                          <h5 class="attachment-title">专家处理附件</h5>
                          <el-tag size="small" type="success" effect="plain">
                            {{ getExpertAttachmentCount(item.remark1) }} 个文件
                          </el-tag>
                        </div>
                        <div class="expert-attachment-list">
                          <div v-for="(fileUrl, index) in parseExpertAttachments(item.remark1)" :key="index"
                            class="expert-attachment-item" @click="downloadExpertFile(fileUrl)">
                            <div class="expert-file-icon">
                              <i :class="getFileIconByUrl(fileUrl)"></i>
                            </div>
                            <div class="expert-file-info">
                              <div class="expert-file-name">{{ getFileNameFromUrl(fileUrl) }}</div>
                              <div class="expert-file-action">点击下载</div>
                            </div>
                            <div class="expert-download-icon">
                              <i class="el-icon-download"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 回访评价 -->
                  <div v-if="isVisitProcess(item.taskName) && consultation.status == 6" class="visit-section">
                    <div class="section-divider"></div>
                    <div class="visit-content">
                      <!-- 满意度评价 -->
                      <div v-if="consultation.status == 6" class="satisfaction-rating">
                        <div class="rating-header">
                          <div class="rating-icon">
                           <el-icon><StarFilled /></el-icon>
                          </div>
                          <h5 class="rating-title">满意度评价</h5>
                        </div>
                        <div class="rating-stars">
                          <el-rate v-model="consultation.satisfaction" disabled  text-color="#ff9900"
                            >
                          </el-rate>
                        </div>
                      </div>

                      <!-- 意见建议 -->
                      <div v-if="consultation.suggestion" class="visit-suggestion">
                        <div class="suggestion-header">
                          <div class="suggestion-icon">
                           <el-icon><EditPen /></el-icon>
                          </div>
                          <h5 class="suggestion-title">意见建议</h5>
                        </div>
                        <div class="suggestion-content">{{ consultation.suggestion }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="empty-timeline">
            <div class="empty-icon">
              <i class="el-icon-document-delete"></i>
            </div>
            <div class="empty-text">暂无咨询记录</div>
            <div class="empty-subtext">当前还没有任何咨询记录信息</div>
          </div>
        </div>
      </div>
    </div>
    <div class="action-footer">
      <el-button size="default" @click="cancelFunc" class="back-btn">
        <i class="el-icon-arrow-left"></i>
        返回上一页
      </el-button>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
export default {
  data() {
    return {
      /*是否加载完成*/
      loading: true,
      // 咨询单基础信息
      consultation: {
        orderCode: '',
      },
      // 咨询单详细记录
      consultationRecordList: [],
    };
  },
  created() {
    this.getParams();
  },
  methods: {
    getParams() {
      const params = this.$route.query;
      feedBack.workOrderDetail(params.id).then(res => {
        console.log(res.data)
        this.consultation = res.data;
        // this.consultation = res.data.categoryId ? res.data.categoryId * 1 : null;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('获取详情失败');
      });
      if (params.status > 5) {
        feedBack.taskHistory({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {


        });
      } else {
        feedBack.taskProgress({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {

        });
      }
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
    getTimelineColor(status) {
      if (status === 0) {
        // 处理中
        return '#409eff';
      }
      if (status === 1) {
        // 已完成
        return '#67c23a';
      }
      return '#909399';
    },
    getTimelineType(status) {
      if (status === 0) {
        return 'primary';
      }
      if (status === 1) {
        return 'success';
      }
      return 'info';
    },
    getStatusType(status) {
      const statusMap = {
        3: 'warning',  // 待专家处理
        4: 'primary',  // 专家处理中
        5: 'info',     // 待回访
        6: 'success',  // 已完成
        7: 'danger',   // 已驳回
        8: 'info'      // 已撤销
      };
      return statusMap[status] || 'info';
    },
    getStatusText(status) {
      const statusTexts = ['', '', '', '待专家处理', '专家处理中', '待回访', '已完成', '已驳回', '已撤销'];
      return statusTexts[status] || '未知状态';
    },
    getRecordStatusType(status) {
      if ((this.consultation.status == 7 || this.consultation.status == 8) && status != '通过') {
        return 'danger';
      }
      if (!status) return 'info';
      const statusStr = status.toString().toLowerCase();
      if (statusStr.includes('完成') || statusStr.includes('成功') || statusStr.includes('通过')) {
        return 'success';
      }
      if (statusStr.includes('处理中') || statusStr.includes('进行中')) {
        return 'primary';
      }
      if (statusStr.includes('驳回') || statusStr.includes('失败')) {
        return 'danger';
      }
      if (statusStr.includes('待') || statusStr.includes('等待')) {
        return 'warning';
      }
      return 'info';
    },
    getCompletedCount() {
      return this.consultationRecordList.filter(item => {
        const status = (item.result || item.status || '').toString().toLowerCase();
        return status.includes('完成') || status.includes('成功');
      }).length;
    },
    getTimelineContentClass(status, index) {
      const baseClass = 'timeline-item';
      // if (status === 3) {
      //   return `${baseClass} completed`;
      // }
      // if (status === 0) {
      //   return `${baseClass} processing`;
      // }
      // if (index === 0) {
      //   return `${baseClass} current`;
      // }
      return baseClass;
    },
    getStatusIcon(status) {
      if (!status) return 'el-icon-info';
      const statusStr = status.toString().toLowerCase();
      if (statusStr.includes('完成') || statusStr.includes('成功')) {
        return 'el-icon-circle-check';
      }
      if (statusStr.includes('处理中') || statusStr.includes('进行中')) {
        return 'el-icon-loading';
      }
      if (statusStr.includes('驳回') || statusStr.includes('失败')) {
        return 'el-icon-circle-close';
      }
      if (statusStr.includes('待') || statusStr.includes('等待')) {
        return 'el-icon-clock';
      }
      return 'el-icon-info';
    },
    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document';
      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-document',
        'xlsx': 'el-icon-document',
        'ppt': 'el-icon-document',
        'pptx': 'el-icon-document',
        'txt': 'el-icon-document',
        'zip': 'el-icon-folder',
        'rar': 'el-icon-folder'
      };
      return iconMap[ext] || 'el-icon-document';
    },
    // 下载文件
    downloadFile(url, fileName) {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 判断是否为专家处理流程
    isExpertProcess(taskName) {
      return taskName && (taskName.includes('专家') || taskName.includes('处理'));
    },
    // 判断是否为回访流程
    isVisitProcess(taskName) {
      return taskName && taskName.includes('回访');
    },
    // 解析专家附件
    parseExpertAttachments(remark1) {
      if (!remark1) return [];
      return remark1.split(',').filter(url => url.trim());
    },
    // 获取专家附件数量
    getExpertAttachmentCount(remark1) {
      return this.parseExpertAttachments(remark1).length;
    },
    // 根据URL获取文件图标
    getFileIconByUrl(url) {
      if (!url) return 'el-icon-document';
      const fileName = url.split('/').pop();
      return this.getFileIcon(fileName);
    },
    // 从URL获取文件名
    getFileNameFromUrl(url) {
      if (!url) return '未知文件';
      const fileName = url.split('/').pop();
      // 移除时间戳前缀，保留原始文件名
      const match = fileName.match(/^\d+\.(.+)$/);
      return match ? match[1] : fileName;
    },
    // 下载专家文件
    downloadExpertFile(url) {
      const fileName = this.getFileNameFromUrl(url);
      this.downloadFile(url, fileName);
    }
  }
};
</script>
<style scoped>
.feedback-detail-container {
  padding: 24px;
  background-color: #f4f4f4;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  min-height: calc(100vh - 120px);
  position: relative;
}

.feedback-detail-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>'); */
  /* pointer-events: none; */
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.info-section,
.timeline-section,
.description-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 20px;
  position: relative;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  padding: 16px 0;
  position: relative;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
  animation: pulse 2s infinite;
}

.title-icon i {
  color: #ffffff;
  font-size: 20px;
}

.title-text {
  flex: 1;
  color: #000;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-decoration {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, transparent);
  border-radius: 2px;
  margin-left: 16px;
}

.timeline-stats {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.stats-item {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 24px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
}

.info-card,
.timeline-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.card-background,
.timeline-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
  pointer-events: none;
}

.info-item {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid rgba(64, 158, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #409eff, #67c23a);
  transition: width 0.3s ease;
}

.info-item:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.3);
}

.info-item:hover::before {
  width: 6px;
}

.info-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.customer-info .info-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.time-info .info-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.contact-info .info-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.phone-info .info-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.status-info .info-icon {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.finish-info .info-icon {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.info-icon i {
  color: #ffffff;
  font-size: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
  word-break: break-all;
  line-height: 1.4;
}

.status-tag {
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.custom-timeline {
  position: relative;
}

.timeline-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(64, 158, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-left: 20px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 24px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  box-shadow: 0 16px 32px rgba(64, 158, 255, 0.15);
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(64, 158, 255, 0.3);
}

.timeline-content:hover::before {
  border-right-color: rgba(64, 158, 255, 0.3);
}

.timeline-step {
  position: absolute;
  top: -12px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  animation: bounce 2s infinite;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid rgba(64, 158, 255, 0.1);
}

.timeline-title-wrapper {
  flex: 1;
}

.timeline-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
}

.timeline-subtitle {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.timeline-status-tag {
  margin-left: 16px;
  font-weight: 600;
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.timeline-details {
  margin-top: 16px;
}

.timeline-item.completed {
  background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
  border-color: rgba(34, 197, 94, 0.2);
}

.timeline-item.processing {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: rgba(59, 130, 246, 0.2);
}

.timeline-item.current {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border-color: rgba(245, 158, 11, 0.2);
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: translateX(4px);
}

.detail-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.detail-icon i {
  color: #ffffff;
  font-size: 14px;
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
  line-height: 1.4;
}

.empty-timeline {
  text-align: center;
  padding: 60px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  border: 2px dashed rgba(148, 163, 184, 0.3);
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
}

.empty-icon i {
  font-size: 32px;
  color: #94a3b8;
}

.empty-text {
  font-size: 18px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #94a3b8;
}

.action-footer {
  text-align: center;
  margin-top: 40px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-btn {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: #ffffff;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.back-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.back-btn i {
  margin-right: 8px;
  font-size: 16px;
}

/* Element Plus Timeline 样式覆盖 */
:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: #6b7280;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

:deep(.el-timeline-item__node) {
  border-width: 4px;
  width: 16px;
  height: 16px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 32px;
}

:deep(.el-timeline-item__tail) {
  border-left: 3px solid rgba(64, 158, 255, 0.2);
}

/* 动画效果 */
@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-4px);
  }

  60% {
    transform: translateY(-2px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 加载动画 */
.info-item {
  animation: fadeInUp 0.6s ease-out;
}

.info-item:nth-child(1) {
  animation-delay: 0.1s;
}

.info-item:nth-child(2) {
  animation-delay: 0.2s;
}

.info-item:nth-child(3) {
  animation-delay: 0.3s;
}

.info-item:nth-child(4) {
  animation-delay: 0.4s;
}

.info-item:nth-child(5) {
  animation-delay: 0.5s;
}

.info-item:nth-child(6) {
  animation-delay: 0.6s;
}

.timeline-content {
  animation: slideInLeft 0.8s ease-out;
}

.timeline-content:nth-child(1) {
  animation-delay: 0.2s;
}

.timeline-content:nth-child(2) {
  animation-delay: 0.4s;
}

.timeline-content:nth-child(3) {
  animation-delay: 0.6s;
}

.timeline-content:nth-child(4) {
  animation-delay: 0.8s;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .detail-content {
    max-width: 100%;
    padding: 0 16px;
  }

  .timeline-stats {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .feedback-detail-container {
    padding: 16px;
  }

  .info-card,
  .timeline-card {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .title-icon {
    width: 40px;
    height: 40px;
    margin-right: 0;
  }

  .title-decoration {
    width: 100%;
    margin-left: 0;
  }

  .timeline-content {
    margin-left: 16px;
    padding: 16px;
  }

  .timeline-content::before {
    left: -8px;
    border-right-width: 8px;
  }

  .timeline-step {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .info-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .info-icon {
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .timeline-status-tag {
    margin-left: 0;
  }

  .detail-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .detail-icon {
    margin-right: 0;
  }
}

/* 状态标签样式增强 */
:deep(.el-tag) {
  font-weight: 600;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* 任务描述和附件样式 */
.description-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.description-content,
.attachment-content,
.no-attachment {
  margin-bottom: 24px;
}

.description-content:last-child,
.attachment-content:last-child,
.no-attachment:last-child {
  margin-bottom: 0;
}

.content-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.content-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.content-icon i {
  color: #ffffff;
  font-size: 18px;
}

.content-title {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  flex: 1;
}

.description-text {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  border-left: 4px solid #667eea;
  margin-left: 52px;
}

.attachment-list {
  margin-left: 52px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.attachment-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.attachment-item:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.15);
}

.file-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon i {
  color: #ffffff;
  font-size: 18px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-action {
  font-size: 12px;
  color: #6b7280;
}

.download-icon {
  width: 32px;
  height: 32px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.download-icon i {
  color: #409eff;
  font-size: 16px;
}

.empty-attachment {
  margin-left: 52px;
  text-align: center;
  padding: 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 2px dashed rgba(148, 163, 184, 0.3);
}

.empty-attachment i {
  font-size: 32px;
  color: #94a3b8;
  margin-bottom: 8px;
}

.empty-attachment span {
  font-size: 14px;
  color: #64748b;
}

/* 专家处理和回访评价样式 */
.expert-section,
.visit-section {
  margin-top: 20px;
}

.section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.3), transparent);
  margin: 16px 0;
}

.expert-content,
.visit-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.expert-remark,
.expert-attachments,
.satisfaction-rating,
.visit-suggestion {
  margin-bottom: 20px;
}

.expert-remark:last-child,
.expert-attachments:last-child,
.satisfaction-rating:last-child,
.visit-suggestion:last-child {
  margin-bottom: 0;
}

.remark-header,
.attachment-header,
.rating-header,
.suggestion-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.remark-icon,
.attachment-icon,
.rating-icon,
.suggestion-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.remark-icon i,
.attachment-icon i,
.rating-icon i,
.suggestion-icon i {
  color: #ffffff;
  font-size: 14px;
}

.remark-title,
.attachment-title,
.rating-title,
.suggestion-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.remark-content,
.suggestion-content {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  border-left: 3px solid #667eea;
}

.expert-attachment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.expert-attachment-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expert-attachment-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.expert-file-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.expert-file-icon i {
  color: #ffffff;
  font-size: 14px;
}

.expert-file-info {
  flex: 1;
}

.expert-file-name {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
  word-break: break-all;
}

.expert-file-action {
  font-size: 11px;
  color: #6b7280;
}

.expert-download-icon {
  width: 24px;
  height: 24px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.expert-download-icon i {
  color: #409eff;
  font-size: 12px;
}

.rating-stars {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 768px) {

  .attachment-list,
  .expert-attachment-list {
    grid-template-columns: 1fr;
  }

  .description-text,
  .empty-attachment {
    margin-left: 0;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .content-icon {
    width: 36px;
    height: 36px;
  }
}
</style>
