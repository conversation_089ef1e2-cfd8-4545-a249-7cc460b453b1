<!--查看咨询单详情-->
<template>
  <div class="feedback-detail-container" v-loading="loading">
    <!--内容-->
    <div class="detail-content">
      <!--基本信息-->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">
            <i class="el-icon-document"></i>
            基本信息
          </div>
        </div>
        <div class="info-card">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">客户名称</div>
                <div class="info-value">{{ consultation.customerName || '--' }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">创建时间</div>
                <div class="info-value">{{ consultation.createTime || '--' }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">联系人姓名</div>
                <div class="info-value">{{ consultation.contactName || '--' }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">联系方式</div>
                <div class="info-value">{{ consultation.contactPhone || '--' }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">当前状态</div>
                <div class="info-value">
                  <el-tag
                    :type="getStatusType(consultation.status)"
                    :effect="consultation.status === 6 ? 'dark' : 'light'"
                    size="small">
                    {{ getStatusText(consultation.status) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">完成时间</div>
                <div class="info-value">{{ consultation.feedbackTime || '--' }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <!--展示咨询单记录-->
      <div class="timeline-section">
        <div class="section-header">
          <div class="section-title">
            <i class="el-icon-time"></i>
            咨询单记录
          </div>
        </div>
        <div class="timeline-card">
          <el-timeline v-if="consultationRecordList.length > 0">
            <el-timeline-item
              v-for="(item, index) in consultationRecordList"
              :key="index"
              :timestamp="item.createTime"
              :hide-timestamp="false"
              :color="getTimelineColor(item.finishStatus)"
              :size="index === 0 ? 'large' : 'normal'"
              :type="getTimelineType(item.finishStatus)">
              <div class="timeline-content">
                <div class="timeline-header">
                  <h4 class="timeline-title">{{ item.taskName }}</h4>
                  <el-tag
                    :type="getRecordStatusType(item.result || item.status)"
                    size="small"
                    effect="light">
                    {{ item.result || item.status }}
                  </el-tag>
                </div>
                <div class="timeline-details">
                  <el-row :gutter="16">
                    <el-col :span="8" v-if="$route.query.status==6">
                      <div class="detail-item">
                        <span class="detail-label">开始时间:</span>
                        <span class="detail-value">{{ item.startTime || '--' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8" v-if="$route.query.status==6">
                      <div class="detail-item">
                        <span class="detail-label">完成时间:</span>
                        <span class="detail-value">{{ item.endTime || '--' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8" v-else>
                      <div class="detail-item">
                        <span class="detail-label">审核时间:</span>
                        <span class="detail-value">{{ item.auditTime || '--' }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="empty-timeline">
            <el-empty description="暂无记录" :image-size="100"></el-empty>
          </div>
        </div>
      </div>
    </div>
    <div class="action-footer">
      <el-button size="default" @click="cancelFunc" class="back-btn">
        <i class="el-icon-arrow-left"></i>
        返回上一页
      </el-button>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
export default {
  data() {
    return {
      /*是否加载完成*/
      loading: true,
      // 咨询单基础信息
      consultation: {
        orderCode: '',
      },
      // 咨询单详细记录
      consultationRecordList: [],
    };
  },
  created() {
    this.getParams();
  },
  methods: {
    getParams() {
      const params = this.$route.query;
      feedBack.workOrderDetail(params.id).then(res => {
        console.log(res.data)
        this.consultation = res.data;
        // this.consultation = res.data.categoryId ? res.data.categoryId * 1 : null;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('获取详情失败');
      });
      if (this.$route.query.status == 6) {
        feedBack.taskHistory({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {


        });
      } else {
        feedBack.taskProgress({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {

        });
      }
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
    getTimelineColor(status) {
      if (status === 0) {
        // 处理中
        return '#409eff';
      }
      if (status === 1) {
        // 已完成
        return '#67c23a';
      }
      return '#909399';
    },
    getTimelineType(status) {
      if (status === 0) {
        return 'primary';
      }
      if (status === 1) {
        return 'success';
      }
      return 'info';
    },
    getStatusType(status) {
      const statusMap = {
        3: 'warning',  // 待专家处理
        4: 'primary',  // 专家处理中
        5: 'info',     // 待回访
        6: 'success',  // 已完成
        7: 'danger',   // 已驳回
        8: 'info'      // 已撤销
      };
      return statusMap[status] || 'info';
    },
    getStatusText(status) {
      const statusTexts = ['', '', '', '待专家处理', '专家处理中', '待回访', '已完成', '已驳回', '已撤销'];
      return statusTexts[status] || '未知状态';
    },
    getRecordStatusType(status) {
      if (!status) return 'info';
      const statusStr = status.toString().toLowerCase();
      if (statusStr.includes('完成') || statusStr.includes('成功')) {
        return 'success';
      }
      if (statusStr.includes('处理中') || statusStr.includes('进行中')) {
        return 'primary';
      }
      if (statusStr.includes('驳回') || statusStr.includes('失败')) {
        return 'danger';
      }
      if (statusStr.includes('待') || statusStr.includes('等待')) {
        return 'warning';
      }
      return 'info';
    }
  }
};
</script>
<style scoped>
.feedback-detail-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
}

.info-section, .timeline-section {
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding: 12px 0;
}

.section-title i {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.info-card, .timeline-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid #ebeef5;
}

.info-item {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.info-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  word-break: break-all;
}

.timeline-content {
  background: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  margin-left: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 8px solid #ebeef5;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f2f5;
}

.timeline-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.timeline-details {
  margin-top: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
  min-width: 70px;
  font-weight: 500;
}

.detail-value {
  font-size: 13px;
  color: #606266;
  flex: 1;
}

.empty-timeline {
  text-align: center;
  padding: 40px 0;
}

.action-footer {
  text-align: center;
  margin-top: 32px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.back-btn {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.back-btn i {
  margin-right: 6px;
}

/* Element Plus Timeline 样式覆盖 */
:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

:deep(.el-timeline-item__node) {
  border-width: 3px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-detail-container {
    padding: 12px;
  }

  .info-card, .timeline-card {
    padding: 16px;
  }

  .timeline-content {
    margin-left: 8px;
  }

  .timeline-content::before {
    left: -6px;
    border-right-width: 6px;
  }
}

/* 加载动画 */
.info-item, .timeline-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 状态标签样式增强 */
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}
</style>
