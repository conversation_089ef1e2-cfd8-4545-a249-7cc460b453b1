<!--查看咨询单详情-->
<template>
  <div class="pb50" v-loading="loading">
    <!--内容-->
    <div class="product-content">
      <!--基本信息-->
      <div class="common-form">基本信息</div>
      <div class="table-wrap">
        <!-- {{ consultation }} -->
        <el-row>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">客户名称：</span>
              {{ consultation.customerName }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">创建时间：</span>
              {{ consultation.createTime }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">联系人姓名：</span>
              {{ consultation.contactName }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">联系方式：</span>
              {{ consultation.contactPhone }}
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">当前状态：</span>
              <span> {{ ['', '', '', '待专家处理', '专家处理中', '待回访', '已完成', '已驳回', '已撤销'][consultation.status] || '未知状态'
              }}</span>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="pb16">
              <span class="gray9">完成时间：</span>
              {{ consultation.feedbackTime || '--' }}
            </div>
          </el-col>
        </el-row>
      </div>
      <!--展示咨询单记录-->
      <div>
        <div class="common-form mt16">咨询单记录</div>
        <div class="table-wrap">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in consultationRecordList" :key="index" :timestamp="item.createTime"
              :hide-timestamp="true" :color="getTimelineColor(item.finishStatus)">
              <el-row>
                <!--<el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">咨询单记录编码:</span>
                    {{item.consultationRecordId}}
                  </div>
                </el-col>-->
                <!-- <el-col :span="3">
                  <div class="pb16">
                    <span class="gray9">用户姓名:</span>
                    {{ item.supplierUserRealName }}
                  </div>
                </el-col> -->
                <el-col :span="6">
                  <div class="pb16">
                    <span class="gray9">流程类型:</span>
                    <span>{{ item.taskName }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="pb16">
                    <span class="gray9">状态:</span>
                    <span>{{ item.result || item.status }}</span>
                  </div>
                </el-col>
                <!-- <el-col :span="6">
                  <div class="pb16">
                    <span class="gray9">意见或建议:</span>
                    {{ item.opinion }}
                  </div>
                </el-col> -->
                <el-col :span="6"  v-if="$route.query.status==6">
                  <div class="pb16">
                    <span class="gray9">开始时间:</span>
                    {{ item.startTime }}
                  </div>
                </el-col>
                <el-col :span="6"  v-if="$route.query.status==6">
                  <div class="pb16">
                    <span class="gray9">完成时间:</span>
                    {{ item.endTime }}
                  </div>
                </el-col>
                <el-col :span="8" v-else>
                  <div class="pb16">
                    <span class="gray9">完成时间:</span>
                    {{ item.endTime }}
                  </div>
                </el-col>
              </el-row>
            </el-timeline-item>
          </el-timeline>
          <!--          <el-button type="primary" @click="completeImplementation">
            完成实施
          </el-button>-->
        </div>
      </div>
    </div>
    <div class="common-button-wrapper">
      <el-button size="small" type="info" @click="cancelFunc">返回上一页</el-button>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
export default {
  data() {
    return {
      /*是否加载完成*/
      loading: true,
      // 咨询单基础信息
      consultation: {
        orderCode: '',
      },
      // 咨询单详细记录
      consultationRecordList: [],
    };
  },
  created() {
    this.getParams();
  },
  methods: {
    getParams() {
      const params = this.$route.query;
      feedBack.workOrderDetail(params.id).then(res => {
        console.log(res.data)
        this.consultation = res.data;
        // this.consultation = res.data.categoryId ? res.data.categoryId * 1 : null;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('获取详情失败');
      });
      if (this.$route.query.status == 6) {
        feedBack.taskHistory({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {


        });
      } else {
        feedBack.taskProgress({ pid: params.pid }).then(res => {
          this.consultationRecordList = res.data
        }).catch(error => {

        });
      }
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
    getTimelineColor(status) {
      if (status === 0) {
        // 处理中
        return '#7d7d7d';
      }
      if (status === 1) {
        // 已完成
        return '#0bbd87';
      }
      return null;
    }
  }
};
</script>
<style></style>
