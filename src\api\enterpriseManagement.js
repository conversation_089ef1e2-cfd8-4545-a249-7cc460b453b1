import request from '@/utils/request'
let EnterpriseManagementApi = {
    // 查询企业信息列表
    getList(data, errorback) {
        return request._postBody('/supplier/qiTuoZhongTai/enterpriseManagement/list', data, errorback);
    },
    // 用id取企业信息详情
    getDetail(data, errorback) {
        return request._post('/supplier/qiTuoZhongTai/enterpriseManagement/detail', data, errorback);
    },
    // 修改
    update(data, errorback) {
        return request._postBody('/supplier/qiTuoZhongTai/enterpriseManagement/update', data, errorback);
    },
    // 删除
    delete(data, errorback){
        return request._postBody('/supplier/qiTuoZhongTai/enterpriseManagement/delete', data, errorback);
    },
}

export default EnterpriseManagementApi;
