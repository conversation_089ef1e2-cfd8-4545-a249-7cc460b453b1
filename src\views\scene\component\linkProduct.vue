<template>
    <el-button :type="modelValue ? 'success' : 'primary'"
        @click="onDialogOpen">{{ modelValue ? `已关联：${label}` : '关联产品' }}</el-button>
    <el-dialog v-model="dialog.show" title="选择关联产品" width="800">
        <div v-loading="dialog.loading">
            <div class="filter">
                <el-form size="small" :inline="true">
                    <el-form-item label="商品分类">
                        <el-select size="small" v-model="dialog.filter.categoryId" placeholder="所有分类"
                            style="width:100px">
                            <el-option label="全部" value="0"></el-option>
                            <el-option v-for="(item, index) in dialog.category" :key="index" :label="item.name"
                                :value="item.categoryId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="商品名称">
                        <el-input size="small" v-model="dialog.filter.productName" placeholder="请输入商品名称"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="small" type="primary" icon="Search" @click="onSearch">查询</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="dialog.data">
                <el-table-column property="productName" label="产品名称" />
                <el-table-column property="categoryName" label="分类" width="100" />
                <el-table-column label="图片" width="100">
                    <template #default="scope">
                        <el-image style="width: 50px; height: 50px" :src="scope.row.imagePath" :zoom-rate="1.2"
                            :max-scale="7" :min-scale="0.2" :preview-src-list="[scope.row.imagePath]" fit="cover" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="onProductSelected(scope.row)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!--分页-->
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
                    :current-page="dialog.pagination.pageIndex" :page-size="dialog.pagination.pageSize"
                    :total="dialog.pagination.total" layout="total, prev, pager, next, jumper"></el-pagination>
            </div>
        </div>
    </el-dialog>
</template>
<script setup>
import { onMounted, reactive } from 'vue';
import PorductApi from '@/api/product.js';

const props = defineProps({
    modelValue: Number,
    label: String

})
const emit = defineEmits(['update:modelValue', 'update:label'])

const dialog = reactive({
    show: false,
    loading: false,
    data: [],
    category: [],
    filter: {
        categoryId: "0",
        activeName: 'sell'
    },
    pagination: {
        total: 0,
        pageIndex: 1,
        pageSize: 10
    }
})
const onDialogOpen = () => {
    dialog.show = true
}
const onProductSelected = (row) => {
    dialog.show = false
    emit('update:modelValue', row.productId)
    emit('update:label', row.productName)
}
const onSearch = () => {
    let p = { ...dialog.filter, ...dialog.pagination };
    dialog.loading = true;
    PorductApi.productList(p, true)
        .then(res => {
            dialog.loading = false;
            dialog.data = res.data.productList.records;
            dialog.category = res.data.categoryList;
            dialog.pagination.total = res.data.productList.total;
        })
        .catch(error => {
            dialog.loading = false;
        });
}
/*选择第几页*/
const handleCurrentChange = (val) => {
    dialog.pagination.pageIndex = val;
    onSearch()
}
/*每页多少条*/
const handleSizeChange = (val) => {
    dialog.pagination.pageSize = val;
    onSearch()
}


onMounted(() => {
    onSearch();
})
</script>
<style lang="scss" scoped></style>