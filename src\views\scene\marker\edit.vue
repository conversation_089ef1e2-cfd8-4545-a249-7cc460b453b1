<template>
    <div class="product-add pb50">
        <!--添加标记-->
        <el-form size="small" :model="form" ref="refForm" :rules="rules" label-width="200px">
            <div class="common-form">添加标记</div>
            <el-form-item label="标记名称" prop="enterpriseMapName">
                <el-input v-model="form.enterpriseMapName" placeholder="请输入标记名称" class="max-w460"></el-input>
            </el-form-item>
            <el-form-item label="标记提示" >
                <el-input v-model="form.enterpriseMapLabel" placeholder="请输入标记提示" class="max-w460"></el-input>
            </el-form-item>
            <el-form-item label="标记坐标（X轴）" >
                <el-input-number v-model="form.xaxis" class="max-w460" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="标记坐标（Y轴）">
                <el-input-number v-model="form.yaxis" class="max-w460" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="关联产品" prop="productId">
                <linkProduct v-model="form.productId" v-model:label="form.productName" />
            </el-form-item>
            <el-form-item label="成功案例">
                <uploadImgs v-model="form.successCase" />
            </el-form-item>
            <el-form-item label="演示视频">
                <uploadVideo v-model="form.demoVideo" />
            </el-form-item>
            <el-form-item label="销售方案">
                <uploadImgs v-model="form.salesPlan" />
            </el-form-item>
            <el-form-item label="线索">
                <uploadImgs v-model="form.appScenario" />
            </el-form-item>
            <el-form-item label="客户目标">
                <uploadImgs v-model="form.goalCustomer" />
            </el-form-item>
            <el-form-item label="价格">
                <uploadImgs v-model="form.quotation" />
            </el-form-item>

            <!--提交-->
            <div class="common-button-wrapper">
                <el-button size="small" type="info" @click="onCacnel">取消</el-button>
                <el-button size="small" type="primary" @click="onSubmit(refForm)" :loading="loading">提交</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup>
import uploadImgs from "../component/uploadImgs.vue";
import uploadVideo from "../component/uploadVideo.vue";
import linkProduct from "../component/linkProduct.vue";
import { useRouter, useRoute } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import sceneApi from "@/api/scene.js";

const route = useRoute();
const router = useRouter();

const refForm = ref(null);

const loading = ref(false);
const form = reactive({
    id:"",
    enterpriseMapName: "",
    enterpriseMapLabel: "",
    xaxis: 0,
    yaxis: 0,

    productId: undefined,
    productName: "",

    successCase: [],
    demoVideo: "",
    salesPlan: [],
    appScenario: [],
    goalCustomer: [],
    quotation: [],
    appId: "10001",
});
const rules = reactive({
    productId: { required: true, message: '请选择关联产品', trigger: 'change' },
    enterpriseMapName: { required: true, message: '请输入标记名称', trigger: 'blur' },
})
const onCacnel = () => {
    router.push('/scene/marker/index')

}
const onSubmit = async (refForm) => {
    if (!refForm) return
    await refForm.validate((valid, fields) => {
        if (valid) {
            sceneApi.editMarker(form).then(res=>{
                ElMessage({ message: '修改成功', type: 'success' });
              router.push('/scene/marker/index');
            })
        }
    })
    
}

onMounted(() => {
    let id = route.query.id
    sceneApi.getMarker({id}).then(res => {
        const splitStr = (v) =>v?v.split(","):[]
        let data = {
            ...res.data,
            successCase: splitStr(res.data.successCase),
            salesPlan: splitStr(res.data.salesPlan),
            appScenario: splitStr(res.data.appScenario),
            goalCustomer: splitStr(res.data.goalCustomer),
            quotation: splitStr(res.data.quotation)
        }
        Object.keys(data).forEach(key => {
            form[key] = data[key];
        })

    })

})
</script>

<style>
.edit_container {
    font-family: "Avenir", Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
}

.ql-editor {
    height: 400px;
}
</style>