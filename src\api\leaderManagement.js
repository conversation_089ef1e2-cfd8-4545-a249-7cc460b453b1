import request from '@/utils/request'
let leaderManagementApi = {
    // 列表 参数:userId
    getList(data, errorback) {
        return request._get('/supplier/enterprise/claim/list', data, errorback);
    },
    // 详情 参数:id
    getDetail(data, errorback) {
        return request._get('/supplier/enterprise/claim/list/detils', data, errorback);
    },
    // 拜访记录 参数:claimId
    getLogs(data, errorback) {
        return request._get('/supplier/enterprise/claim/visitLog/list', data, errorback);
    },
    // 增加或修改拜访图片的 参数: claimId 关联认领ID  visitImages:拜访图片 visitDate:拜访日期
    saveLog(data, errorback){
        return request._get('/supplier/enterprise/claim/visitLog/save', data, errorback);
    },

     listSum(data, errorback){
        return request._get('/supplier/enterprise/claim/list/sum', data, errorback);
    },

      jieChu(data, errorback){
        return request._get('/supplier/enterprise/claim/jiechu', data, errorback);
    },
}

export default leaderManagementApi;
