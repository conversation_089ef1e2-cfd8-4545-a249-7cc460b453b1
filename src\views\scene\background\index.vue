<template>
    <el-form ref="refForm" :model="form.data" :rules="form.rules">
        <el-form-item label="背景图片" prop="img">
            <div v-if="form.data.img" class="scene-background" @click="handleUploadOpen">
                <img :src="form.data.img" width="100" height="100" />
            </div>
            <div class="scene-background select" v-else @click="handleUploadOpen">
                <el-icon>
                    <Plus />
                </el-icon>
            </div>
        </el-form-item>
        <!--提交-->
        <div class="common-button-wrapper">
            <el-button size="small" type="primary" @click="handleSubmit(refForm)"
                :disabled="form.loading">提交</el-button>
        </div>
    </el-form>

    <Upload v-if="uploadDialog.show" :config="uploadDialog.config" :isupload="true" @returnImgs="handleUploadClose">上传图片
    </Upload>
</template>

<script setup>
import { reactive, ref } from 'vue';
import Upload from '@/components/file/Upload.vue';

const refForm = ref(null)
const form = reactive({
    data: {
        img: ''
    },
    rules: {
        img: { required: true, message: '请选择背景图片', trigger: 'change' }
    },
    loading: false
})
const handleSubmit = async (refForm) => {
    if (!refForm) return
    await refForm.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        }
    })
}

const uploadDialog = reactive({
    show: false,
    config: {
        total: 9,
        fileType: 'image'
    }
})
const handleUploadOpen = () => {
    uploadDialog.show = true
}
const handleUploadClose = (e) => {
    if (e && e[0]) {
        form.data.img = e[0].filePath
    }
    uploadDialog.show = false
}



</script>

<style lang='scss' scoped>
.scene-background {
    width: 110px;
    height: 110px;
    cursor: pointer;
    user-select: none;

    &.select {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px dashed #dddddd;
        font-size: 30px;
    }
}
</style>