<!--企业管理页面-->
<template>
  <div class="user">
    <!--搜索表单-->
    <div class="common-seach-wrap">
      <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="客户名称">
          <el-input size="small" clearable v-model="searchForm.customerName" placeholder="请输入客户名称"/>
        </el-form-item>
        <el-form-item label="支局">
          <el-input size="small" clearable v-model="searchForm.branchOffice" placeholder="请输入支局"/>
        </el-form-item>
        <el-form-item label="客户经理">
          <el-input size="small" clearable v-model="searchForm.accountManager" placeholder="请输入客户经理"/>
        </el-form-item>
        <el-form-item label="集团P码">
          <el-input size="small" clearable v-model="searchForm.groupPcode" placeholder="请输入集团P码"/>
        </el-form-item>
        <!-- 暂时隐藏
        <el-form-item label="配送方式">
          <el-select size="small" v-model="searchForm.style_id" placeholder="请选择" style="width: 150px">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="(item, index) in exStyle" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起始时间">
          <div class="block">
            <span class="demonstration"></span>
            <el-date-picker size="small" v-model="searchForm.createTime" type="daterange" value-format="YYYY-MM-DD"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </div>
        </el-form-item>
        -->
        <el-form-item>
          <el-button size="small" type="primary" icon="Search" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" @click="resetSearch">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--内容-->
    <div class="product-content">
      <div class="table-wrap">
        <!-- 暂时隐藏
        <el-tabs v-model="activeName" @tab-change="handleClick">
          <el-tab-pane label="待施工" name="waitingForImplementation"></el-tab-pane>
          <el-tab-pane label="已施工" name="implementationCompleted"></el-tab-pane>
        </el-tabs>
        -->
        <el-table size="small" :data="tableData" border style="width: 100%"
          v-loading="loading">
          <el-table-column prop="id" label="id"/>
          <el-table-column prop="city" label="地市"/>
          <el-table-column prop="customerName" label="客户名称"/>
          <el-table-column prop="area" label="区县"/>
          <el-table-column prop="branchOffice" label="支局"/>
          <el-table-column prop="accountManager" label="客户经理"/>
          <el-table-column prop="contactNumber" label="联系电话"/>
          <el-table-column prop="unifiedSocialCreditCode" label="社会统一信用代码"/>
          <el-table-column prop="customerAttribute" label="客户属性"/>
          <el-table-column prop="lockStatus" label="锁定情况"/>
          <el-table-column prop="address" label="地址"/>
          <el-table-column prop="groupPcode" label="集团P码"/>
          <el-table-column prop="increaseType" label="存增类型"/>
          <el-table-column prop="amount" label="金额"/>
          <el-table-column prop="enterpriseStatus" label="企业状态">
            <template #default="scope">
              <span>
                {{this.convertEnterpriseStatus(scope.row.enterpriseStatus)}}
              </span>
            </template>
          </el-table-column>
          <!-- 都是空的，意义不大
          <el-table-column prop="startTime" label="开始时间"/>
          <el-table-column prop="updateTime" label="更新时间"/>
          -->
          <!--预留字段
          <el-table-column prop="remark1" label="备注1"/>
          <el-table-column prop="remark2" label="备注2"/>
          <el-table-column prop="remark3" label="备注3"/>
          <el-table-column prop="remark4" label="备注4"/>
          -->
          <el-table-column fixed="right" label="操作" width="130">
            <template #default="scope">
              <el-button @click="editClick(scope.row)" link :type="'primary'" size="small">修改</el-button>
              <el-button @click="deleteClick(scope.row)" link :type="'primary'" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页-->
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
          :current-page="curPage" :page-size="pageSize" layout="total, prev, pager, next, jumper"
          :total="totalDataNumber"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
  import EnterpriseManagementApi from "@/api/enterpriseManagement.js";
  import qs from 'qs';
  import { useUserStore } from '@/store';
  import {ElMessage, ElMessageBox} from "element-plus";
  const { token } = useUserStore();
  export default {
    components: {
    },
    data() {
      return {
        token,
        /*切换菜单的初始页名称*/
        activeName: 'waitingForImplementation',
        /*是否加载完成*/
        loading: true,
        /*列表数据*/
        tableData: [],
        /*一页多少条*/
        pageSize: 10,
        /*一共多少条数据*/
        totalDataNumber: 0,
        /*当前是第几页*/
        curPage: 1,
        /*搜索栏数据*/
        searchForm: {
          customerName: '',
          groupPcode: '',
          accountManager: '',
          branchOffice: '',
        },
        /*配送方式*/
        exStyle: [],
        /*门店列表*/
        shopList: [],
        /*时间*/
        create_time: '',
        /*统计*/
        order_count: {
          payment: 0,
          delivery: 0,
          received: 0,
          waitingForImplementation: 0,
        },
        /*当前编辑的对象*/
        order_no: ''
      };
    },
    created() {
      /*获取列表*/
      this.getData();
    },
    methods: {
      /*选择第几页*/
      handleCurrentChange(val) {
        let self = this;
        self.curPage = val;
        self.getData();
      },

      /*每页多少条*/
      handleSizeChange(val) {
        this.curPage = 1;
        this.pageSize = val;
        this.getData();
      },

      /*切换菜单*/
      handleClick(tab) {
        let self = this;
        self.curPage = 1;
        self.activeName = tab;
        self.getData();
      },

      /*查询列表*/
      getData() {
        this.tableData = [];
        let self = this;
        let Params = {};
        Params.pageIndex = self.curPage;
        Params.pageSize = self.pageSize;
        Params.customerName = this.searchForm.customerName;
        Params.groupPcode = this.searchForm.groupPcode;
        Params.accountManager = this.searchForm.accountManager;
        Params.branchOffice = this.searchForm.branchOffice;
        self.loading = true;
        EnterpriseManagementApi.getList(Params, true).then(res => {
          self.tableData = res.data.records;
          self.totalDataNumber = res.data.total;
          self.loading = false;
        }).catch(error => {});
      },
      /*打开添加*/
      addClick(row) {
        let self = this;
        let params = row.orderId;
        self.$router.push({
          path: '/order/orderImplementationRecord/detail',
          query: {
            orderId: params
          }
        });
      },
      // 点击修改按钮
      editClick(row){
        let params = row.id;
        this.$router.push({
          path: '/qiTuoZhongTai/enterpriseManagement/edit',
          query: {
            id: params
          }
        });
      },
      // 点击删除按钮
      deleteClick(row){
        let id = row.id;
        ElMessageBox.confirm(
          `是否确认删除id为${id}的记录?`,
          '警告',
          {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          // 这里增加删除的请求
          EnterpriseManagementApi.delete({ids: [id]}, true).then(res=>{
            if (res.errCode === 200){
              ElMessage({
                type: 'success',
                message: '删除成功',
              })
            }else {
              ElMessage({
                type: 'error',
                message: '删除失败',
              })
            }
            // 删除后刷新页面数据
            this.onSearch();
          });
        }).catch(() => {
          ElMessage({
            type: 'info',
            message: '删除已取消',
          })
        })
      },
      /*搜索查询*/
      onSearch() {
        this.curPage = 1;
        this.getData();
      },
      // 重置搜索
      resetSearch() {
        this.searchForm = {
          customerName: '',
          groupPcode: '',
          accountManager: '',
          branchOffice: '',
        };
        this.onSearch();
      },
      // 导出查询结果到excel文件
      onExport: function() {
        let self = this;
        let Params = {...this.searchForm, 'tokensupplier': self.token};
        let baseUrl = import.meta.env.VITE_BASIC_URL;
        window.location.href = baseUrl + '/api/supplier/qiTuoZhongTai/enterpriseManagement/export?' + qs.stringify(Params);
      },
      // 转换企业状态
      convertEnterpriseStatus(enterpriseStatus){
        if (enterpriseStatus === '1'){
          return "红灯";
        }
        if (enterpriseStatus === '2'){
          return "黄灯";
        }
        if (enterpriseStatus === '3'){
          return "蓝灯";
        }
        if (enterpriseStatus === '4'){
          return "绿灯";
        }
        return enterpriseStatus;
      },
    }
  };
</script>
<style lang="scss">
  .product-info {
    padding: 10px 0;
    border-top: 1px solid #eeeeee;
  }

  .order-code .state-text {
    padding: 2px 4px;
    border-radius: 4px;
    background: #808080;
    color: #ffffff;
    margin-right: 6px;
  }

  .order-code .state-text-red {
    background: red;
  }

  .table-wrap .product-info:first-of-type {
    border-top: none;
  }

  .table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
    background: #f5f7fa;
  }
</style>