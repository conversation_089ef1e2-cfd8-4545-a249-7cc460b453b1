import request from '@/utils/request'
let ConsultationApi = {
    // 查询业务专家列表，不分页
    getBusinessExpertContactInformationListNotPaged(data, errorback) {
        return request._postBody('/supplier/consultation/consultation/getBusinessExpertContactInformationListNotPaged', data, errorback);
    },
    // 用户创建咨询单
    createConsultation(data, errorback) {
        return request._postBody('/supplier/consultation/consultation/createConsultation', data, errorback);
    },
    // 查询页面数据，用于展示
    getPageData(data, errorback) {
        return request._postBody('/supplier/consultation/consultation/getPageData', data, errorback);
    },
    // 查询页面数据，用于专家处理页面展示
    getPageDataForExpertHandling(data, errorback) {
        return request._postBody('/supplier/consultation/consultation/getPageDataForExpertHandling', data, errorback);
    },
    // 用id查询咨询表对象
    getConsultationById(data, errorback) {
        return request._post('/supplier/consultation/consultation/getConsultationById', data, errorback);
    },
}

export default ConsultationApi;
