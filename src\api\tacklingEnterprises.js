import request from '@/utils/request'
let tacklingEnterprisesApi = {
    // 列表 参数:userId
    getList(data, errorback) {
        return request._get('/supplier/enterprise/claim/en/list', data, errorback);
    },
    addEnterprise(data, errorback) {
        return request._get('/supplier/enterprise/claim/en/up/save', data, errorback);
    },
    addUser(data, errorback) {
        return request._get('/supplier/enterprise/claim/user/save', data, errorback);
    },
    getUserList(data, errorback) {
        return request._get('/supplier/enterprise/claim/user/list', data, errorback);
    },
    getEnterpriseList(data, errorback) {
        return request._get('/front/enterprise/claim/en/list?appId=10001&current=1', data, errorback);
    },
    addBind(data, errorback) {
        return request._get('/supplier/enterprise/claim/save/two', data, errorback);
    },


}

export default tacklingEnterprisesApi;
