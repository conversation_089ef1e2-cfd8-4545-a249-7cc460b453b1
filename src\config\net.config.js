export default {
	baseURL: process.env.NODE_ENV === 'development' ? '/api' : `${import.meta.env.VITE_BASIC_URL}/api/api`,
	// baseURL: process.env.NODE_ENV === 'development' ? `${import.meta.env.VITE_BASIC_URL}api/api` : `${import.meta.env.VITE_BASIC_URL}/api/api`,
	tokenName: 'Tokensupplier',
	strongToken: "jjjShopToken",
	renderMenu: "jjjShopRenderMenus",
	menu: "jjjShopMenus",
	isDev: process.env.NODE_ENV === 'development' ? true : false,
	contentType: 'application/x-www-form-urlencoded;charset=UTF-8',
	requestTimeout: 50000,
	successCode: [200, 0, '200', '0'],
	statusName: 'code',
	messageName: 'msg',
	withCredentials: true,
	responseType: 'json',
};