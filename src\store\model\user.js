import { defineStore } from "pinia";
import { setStorage, getStorage } from "@/utils/storageData";
import AuthApi from "@/api/auth.js";
import configObj from "@/config";
let { strongToken, renderMenu, menu } = configObj;
import { handRouterTable, handMenuData } from "@/utils/router";
export const useUserStore = defineStore("main", {
  state: () => {
    return {
      token: getStorage(strongToken),
      userInfo: getStorage("userInfo"),
      list: {},
      menus: getStorage(menu),
      renderMenus: getStorage(renderMenu),
    };
  },
  getters: {},
  actions: {
    bus_on(name, fn) {
      let self = this;
      self.list[name] = self.list[name] || [];
      self.list[name].push(fn);
    },
    // 发布
    bus_emit(name, data) {
      let self = this;
      if (self.list[name]) {
        self.list[name].forEach((fn) => {
          fn(data);
        });
      }
    },
    // 取消订阅
    bus_off(name) {
      let self = this;
      if (self.list[name]) {
        delete self.list[name];
      }
    },
    /**
     * @description 登录
     * @param {*} token
     */
    async afterLogin(info) {
      this.userInfo = this.userInfo || {};
      const {
        data: {
          token,
          loginSupplierUserVo: {
            appId,
            isSuper,
            shopUserId,
            userName,
            supplierUserId,
          },
        },
      } = info;
      this.token = token;
      const { data } = await AuthApi.getRoleList({});
      data[12].name = "反馈";
      data[12].path = "/feedback";
      data[12].children[0].name = "客服中心";
      data[12].children[0].path = "/feedback/index";
      data[12].children[1].name = "专家中心";
      data[12].children[1].path = "/feedback/expert";
      data[12].children[0].children[1] = {
        accessId: 1740465083,
        name: "查看任务详情",
        path: "/feedback/detail",
        parentId: 1739350839,
        sort: 1,
        icon: "",
        redirectName: "",
        isRoute: 1,
        isMenu: 0,
        alias: "",
        isShow: 1,
        plusCategoryId: 0,
        remark: "",
        appId: 10001,
        createTime: "2025-02-25 14:31:23",
        updateTime: "2025-02-25 14:31:45",
        children: [],
      };
      data[12].children[2] = {
        accessId: 1739859786,
        name: "专家列表",
        path: "/feedback/expertList",
        parentId: 1739350319,
        sort: 3,
        icon: "",
        redirectName: "",
        isRoute: 1,
        isMenu: 1,
        alias: "",
        isShow: 1,
        plusCategoryId: 0,
        remark: "",
        appId: 10001,
        createTime: "2025-02-18 14:23:06",
        updateTime: "2025-02-18 14:31:06",
        children: [],
      };
      console.log("roleList", data);
      let renderMenusList = handMenuData(JSON.parse(JSON.stringify(data)));
      let menusList = handRouterTable(JSON.parse(JSON.stringify(data)));
      console.log("menusList", menusList);
      setStorage(JSON.stringify(menusList), menu);
      setStorage(JSON.stringify(renderMenusList), renderMenu);
      this.userInfo.userName = userName;
      // this.userInfo.userId = supplierUserId;
      this.userInfo.shopUserId = shopUserId;
      this.userInfo.isSuper = isSuper;
      this.userInfo.AppID = appId;
      this.renderMenus = renderMenusList;
      this.menus = menusList;
      setStorage(JSON.stringify(token), strongToken);
      setStorage(JSON.stringify(this.userInfo), "userInfo");
      await this.getUserInfo();
    },
    /**
     * @description 获取用户基本信息
     * @param {*} token
     */
    async getUserInfo() {
      const { data } = await AuthApi.getUserInfo({});
      this.userInfo.shop_name = data.shop_name;
      this.userInfo.version = data.version;
      this.userInfo.userId = data.user.userId;
      setStorage(JSON.stringify(this.userInfo), "userInfo");
    },
    /**
     * @description 退出登录
     * @param {*} token
     */
    afterLogout() {
      sessionStorage.clear();
      // deleteSessionStorage(null);
      // delCookie(strongToken,null);
      this.token = null;
      this.menus = null;
    },
  },
});
export default useUserStore;
