import request from '@/utils/request'

export default {
  /*背景地图 - 获取*/
  getBackground(data, errorback) {
    return request._get('/scene/bg', data, errorback);
  },
  /*背景地图 - 保存*/
  saveBackground(data, errorback) {
    return request._postBody('/scene/bg', data, errorback);
  },

  /*标记 - 列表*/
  markerList(data, errorback) {
    return request._postBody('/supplier/scene/listNotPaged', data, errorback);
  },
  addMarker(data, errorback) {
    let p = {
      ...data,
      successCase: data.successCase.join(','),
      salesPlan: data.salesPlan.join(','),
      appScenario: data.appScenario.join(','),
      goalCustomer: data.goalCustomer.join(','),
      quotation: data.quotation.join(',')
    }
    return request._postBody('/supplier/scene/add', p, errorback);
  },
  getMarker (data, errorback) {
    return request._get('/supplier/scene/edit', data, errorback)
  },

  editMarker(data, errorback) {
    let p = {
      ...data,
      successCase: data.successCase.join(','),
      salesPlan: data.salesPlan.join(','),
      appScenario: data.appScenario.join(','),
      goalCustomer: data.goalCustomer.join(','),
      quotation: data.quotation.join(',')
    }
    return request._postBody('/supplier/scene/edit', p, errorback);
  },
  delMarker(data, errorback) {
    return request._post('/supplier/scene/delete', data, errorback);
  },



}