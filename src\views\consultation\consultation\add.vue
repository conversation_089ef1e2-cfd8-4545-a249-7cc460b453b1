<template>
  <div class="product-add">
    <!--form表单-->
    <el-form size="small" ref="formData" :model="formData" label-width="180px">
      <div class="basic-setting-content pl16 pr16">
        <!--基本信息-->
        <div class="common-form">基本信息</div>
        <el-form-item label="选择专家：" :rules="[{ required: true, message: '请选择专家', trigger: 'change' }]">
          <el-select v-model="formData.expertId" placeholder="请选择" class="max-w460"  clearable >
            <el-option v-for="item in expertList" :key="item.id" :label="makeSelectExpertLabel(item)" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="问题内容：">
          <el-input type="textarea" v-model="formData.opinion" :autosize="{ minRows: 20, maxRows: 20}" class="max-w460"/>
        </el-form-item>
      </div>
      <!--提交-->
      <div class="common-button-wrapper">
        <el-button size="small" type="info" @click="cancelFunc">取消</el-button>
        <el-button size="small" type="primary" @click="onSubmit" :loading="loading">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import ConsultationApi from "@/api/consultation.js";
import {ElMessage} from "element-plus";
export default {
  components: {
  },
  data() {
    return {
      /*是否正在加载*/
      loading: false,
      // 专家列表
      expertList: [],
      //form表单数据
      formData:{
        opinion: '',
        expertId: null,
      },
    };
  },
  created() {
    // 这里加一个查询专家用户的接口
    this.initData();
  },
  methods: {
    /*转JSON字符串*/
    convertJson(list) {
      let obj = {};
      list.forEach(item => {
        obj[item.gradeId] = item.productEquity;
      });
      return JSON.stringify(obj);
    },
    // 数据初始化
    initData(){
      // 查询业务专家列表，不分页
      ConsultationApi.getBusinessExpertContactInformationListNotPaged({},true).then(res=>{
        if (res.errCode === 200){
          this.expertList = res.data;
        }
      });
    },
    /*提交*/
    onSubmit() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          console.log(this.formData.expertId);
          if (this.formData.expertId === null){
            ElMessage({
              message: '请选择专家',
              type: 'warning'
            });
            return;
          }
          this.loading = true;
          ConsultationApi.createConsultation(this.formData, true).then(res => {
            this.loading = false;
            ElMessage({
              message: '提交成功',
              type: 'success'
            });
            this.$router.push('/consultation/consultation/index');
          }).catch(error => {
            this.loading = false;
          });
        }
      });
    },
    /*取消*/
    cancelFunc() {
      this.$router.back(-1);
    },
    // 制作选择专家的标签文本
    makeSelectExpertLabel(item){
      // 姓名 电话
      let result = item.name+' - ('+item.phone+')';
      // 业务领域
      if (item.businessArea !== null && item.businessArea !== ''){
        result += (" - "+item.businessArea);
      }
      return result;
    }
  }
};
</script>

<style lang="scss" scoped>
.basic-setting-content {
}

.product-add {
  padding-bottom: 100px;
}
</style>
