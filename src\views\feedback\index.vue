<template>
    <div class="user">
        <!--搜索表单-->
        <div class="common-seach-wrap">
            <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="标题">
                    <el-input size="small" v-model="searchForm.title" placeholder="请输入标题" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" icon="Search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" icon="Plus" @click="addClick">添加反馈</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!--内容-->
        <div class="product-content">
            <div class="table-wrap">
                <el-tabs v-model="activeName" @tab-change="handleClick">
                    <el-tab-pane label="我的任务" name="myTask"></el-tab-pane>
                    <el-tab-pane label="回访待领取" name="awaitVisit"></el-tab-pane>
                    <el-tab-pane label="已完成" name="finished"></el-tab-pane>
                    <el-tab-pane label="数据列表" name="all"></el-tab-pane>
                </el-tabs>
                <el-table size="small" :data="tableData" border style="width: 100%" v-loading="loading">
                    <el-table-column prop="title" label="标题"></el-table-column>
                    <el-table-column prop="customerName" label="客户名称"></el-table-column>
                    <el-table-column prop="categoryId" label="反馈类型">
                        <template #default="scope">
                            <span>
                                {{this.exStyle.find(item => item.categoryId == scope.row.categoryId)?.name || '未知类型'}}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="contactName" label="联系人姓名"></el-table-column>
                    <el-table-column prop="contactPhone" label="联系方式"></el-table-column>
                    <el-table-column prop="currentHandlerName" label="任务专家">
                        <template #default="scope">
                            <span>
                                {{this.businessList.find(item => item.id == scope.row.currentHandlerId)?.name ||
                                    scope.row.currentHandlerId}}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="任务状态">
                        <template #default="scope">
                            <span>
                                {{ ['', '', '',  '待专家处理','专家处理中','待回访', '已完成','已驳回',   '已撤销'][scope.row.status] || '未知状态' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column fixed="right" label="操作" width="130">
                        <template #default="scope">
                            <div v-if="activeName === 'awaitVisit'">
                                <el-button @click="receiveTask(scope.row)" link :type="'primary'"
                                    size="small">领取</el-button>
                            </div>
                            <div v-if="activeName === 'myTask'">
                                <el-button @click="rejectTask(scope.row)" link :type="'primary'"
                                    size="small">驳回</el-button>
                                <el-button @click="endTask(scope.row)" link :type="'primary'"
                                    size="small">任务完成</el-button>
                            </div>
                            <el-button @click="addClickDetail(scope.row)" link :type="'primary'"
                                size="small">详情</el-button>
                            <!-- <el-button @click="updateClickDetail(scope.row)" link :type="'primary'"
                                size="small">修改</el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!--分页-->
            <div class="pagination">
                <el-pagination @current-change="handleCurrentChange" background :current-page="current"
                    :page-size="pageSize" layout="total, prev, pager, next, jumper"
                    :total="totalDataNumber"></el-pagination>
            </div>
        </div>
        <el-dialog title="任务评价" v-model="dialogEvaluate.show" width="400px" :destroy-on-close="true"
            :close-on-click-modal="false">
            <el-form :model="dialogEvaluate.addForm" ref="userFormRef" :rules="rules" label-width="auto"
                label-position="top">
                <el-form-item label="评分" prop="satisfaction">
                    <el-rate v-model="dialogEvaluate.addForm.satisfaction" />
                </el-form-item>

                <el-form-item label="意见" prop="reason">
                    <el-input v-model="dialogEvaluate.addForm.suggestion" :rows="4" type="textarea"
                        placeholder="请填写意见" />
                </el-form-item>
                <div style="display: flex;justify-content: center;">
                    <el-button type="primary" style="width: 150px;" @click="examineTask">完成</el-button>
                    <el-button plan style="width: 150px;" @click="closeAdd">取消</el-button>
                </div>
            </el-form>
        </el-dialog>

        <el-dialog title="添加反馈" v-model="dialogAddFeedback.show" width="800px" :destroy-on-close="true"
            :close-on-click-modal="false">
            <el-form :model="dialogAddFeedback.addForm" ref="userFormRef" :rules="rules" label-width="auto"
                label-position="top">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="客户名称" prop="customerName">
                            <el-select size="medium" filterable remote v-model="dialogAddFeedback.addForm.customerName"
                                clearable placeholder="请选择" :remote-method="remoteMethod" @change="setCustomerName">
                                <el-option v-for="item in userList" :key="item.userId" :label="item.nickname"
                                    :value="item.nickname">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="反馈类型" prop="categoryId">
                            <el-select size="medium" v-model="dialogAddFeedback.addForm.categoryId" clearable
                                placeholder="请选择">
                                <el-option v-for="item in exStyle" :key="item.categoryId" :label="item.name"
                                    :value="item.categoryId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="联系人姓名" prop="contactName">
                            <el-input v-model="dialogAddFeedback.addForm.contactName" placeholder="请输入联系人姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="contactPhone">
                            <el-input v-model="dialogAddFeedback.addForm.contactPhone" placeholder="请输入手机号" />
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="反馈标题" prop="title">
                            <el-input v-model="dialogAddFeedback.addForm.title" placeholder="请输入反馈标题" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="分配专家" prop="currentHandlerId">
                            <el-select size="medium" filterable remote
                                v-model="dialogAddFeedback.addForm.currentHandlerId" clearable placeholder="请选择"
                                :remote-method="remoteBusiness">
                                <el-option v-for="item in businessList" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="反馈内容" prop="description">
                    <el-input v-model="dialogAddFeedback.addForm.description" :rows="4" type="textarea"
                        placeholder="请填写反馈内容" />
                </el-form-item>
                <el-form-item label="上传附件">
                    <el-upload class="avatar-uploader" multiple ref="upload" :limit="3" v-model:file-list="fileList"
                        :before-remove="delFile" :before-upload="onBeforeUploadImage" :http-request="UploadImage">
                        <el-button size="small" icon="Upload" type="primary">点击上传</el-button>
                    </el-upload>
                </el-form-item>

                <div style="display: flex;justify-content: center;">
                    <el-button type="primary" style="width: 150px;" @click="submitAddFeedBack">{{
                        dialogAddFeedback.addForm.id ?
                            '修改' : '添加' }}</el-button>
                    <el-button plan style="width: 150px;" @click="closeAdd">取消</el-button>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
import FileApi from '@/api/file.js';
// import tacklingEnterprises from '@/api/tacklingEnterprises.js';
export default {
    components: {
    },
    data() {
        return {
            userInfo,
            rules: {
                title: [
                    { required: true, message: '请输入反馈标题', trigger: 'blur' },
                    { min: 3, max: 10, message: '长度应为 3 到 10', trigger: 'blur' },
                ],
                customerName: [
                    { required: true, message: '请选择客户名称', trigger: 'change' },
                ],
                contactName: [
                    { required: true, message: '请输入联系人姓名', trigger: 'blur' },

                ],
                contactPhone: [
                    { required: true, message: '请输入联系方式', trigger: 'blur' },

                ],
                categoryId: [
                    {
                        required: true,
                        message: '请选择反馈类型',
                        trigger: 'change',
                    },
                ],
                description: [
                    { required: true, message: '请填写反馈内容', trigger: 'blur' },
                ],
            },
            userList: [],
            businessList: [],
            dialogAddFeedback: {
                show: false,
                addForm: {
                    customerName: '',
                    contactName: '',
                    attachmentList: [],
                    title: '',
                    source: 2,
                    description: '',
                    contactPhone: '',
                    categoryId: '', // 反馈类型
                    feedbackBy: userInfo.userId, // 客服
                }
            },
            dialogEvaluate: {
                show: false,
                addForm: {
                    satisfaction: 0,
                    suggestion: '',
                }
            },
            token,

            /*切换菜单的初始页名称*/
            activeName: 'myTask',
            /*是否加载完成*/
            loading: false,
            /*列表数据*/
            tableData: [],
            /*一页多少条*/
            // pageSize: 10,
            /*一共多少条数据*/
            totalDataNumber: 0,
            /*当前是第几页*/
            current: 1,
            /*横向表单数据模型*/
            searchForm: {
                title: '',
                //createTime: ''
            },
            /*反馈类型*/
            exStyle: [],
            /*统计*/
            order_count: {
                payment: 0,
                delivery: 0,
                received: 0,
                waitingForImplementation: 0,
            },
            /*当前编辑的对象*/
            order_no: '',
            fileList: [],
        };
    },
    created() {
        /*获取列表*/
        this.getType();
        this.remoteMethod('')
        this.remoteBusiness('');
        this.getMyTaskData(userInfo.userId)

    },
    methods: {
        endTask(row) {
            this.dialogEvaluate.show = true;
            this.dialogEvaluate.addForm = row;
            this.dialogEvaluate.addForm.satisfaction = 0;
            this.dialogEvaluate.addForm.suggestion = '';
            // this.dialogEvaluate.addForm.remark = '已经处理完成';
            // this.dialogEvaluate.addForm.remark1 = '附件地址';

            // feedBack.endTask({ cid: row.cid, userId: userInfo.userId }).then(res => {
            //     ElMessage.success('任务已完成');
            //     this.activeName = 'myTask';
            //     this.getMyTaskData(userInfo.userId);
            // }).catch(error => {
            //     ElMessage.error('任务完成失败');
            // });
        },
        examineTask() {
            this.dialogEvaluate.addForm.feedback_time = new Date().getTime();
            feedBack.examineTaskInfoPost(this.dialogEvaluate.addForm).then(res => {
                ElMessage.success('任务完成');
                this.closeAdd();
                this.getMyTaskData(userInfo.userId);
            }).catch(error => {
                ElMessage.error('任务完成失败');
            });
        },
        rejectTask(row) {
            ElMessageBox.prompt('请输入驳回原因', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(({ value }) => {
                    feedBack.rejectTaskInfo({ cid: row.cid, pid: row.processInstanceId, reason: value }).then(res => {
                        ElMessage.success('任务已驳回');
                        this.getMyTaskData(userInfo.userId);
                    }).catch(error => {
                        ElMessage.error('任务驳回失败');
                    });
                })
                .catch(() => {
                    ElMessage({
                        type: 'info',
                        message: '已取消',
                    })
                })
        },
        receiveTask(row) {
            ElMessageBox.confirm(
                '是否确认领取该任务?',
                '提示',
                {
                    confirmButtonText: '通过',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then((res) => {
                feedBack.transferTask({ cid: row.cid, userId: userInfo.userId }).then(res => {
                    ElMessage.success('任务已领取');
                    this.activeName = 'myTask';
                    this.getMyTaskData(userInfo.userId);
                }).catch(error => {
                    ElMessage.error('任务领取失败');
                });
            }).catch((err) => {
                console.log('通过任务', err);
                ElMessage.error('任务领取失败');
            });
        },
        getType() {
            let self = this;
            feedBack.feedbackTypeList().then(res => {
                console.log('获取类型', res);
                self.exStyle = res.data;
            }).catch(error => {
                ElMessage.error('获取类型失败');
            });
        },
        remoteMethod(query) {
            let self = this;
            feedBack.getUserListByName({ name: query }).then(res => {
                console.log('远程查询用户列表', res);
                self.userList = res.data;
            }).catch(error => {
                ElMessage.error('获取用户列表失败');
            });
        },
        remoteBusiness(query) {
            let self = this;
            feedBack.getBusinessListByName({ name: query }).then(res => {
                // console.log('远程查询用户列表', res);
                self.businessList = res.data;
            }).catch(error => {
                ElMessage.error('获取专家列表失败');
            });
        },
        setCustomerName(value) {
            // console.log('设置客户名称', value, this.userList.find(item => item.nickname === value));
            this.dialogAddFeedback.addForm.createdBy = this.userList.find(item => item.nickname === value).userId;
            // this.dialogAddFeedback.addForm.customerName = value;
        },
        UploadImage(param) {
            let self = this;
            const loading = ElLoading.service({
                lock: true,
                text: '文件上传中,请等待',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            const formData = new FormData();
            formData.append('iFile', param.file);
            formData.append("groupId", 0);
            formData.append("fileType", "feedback");
            FileApi.uploadFile(formData)
                .then(response => {
                    console.log('上传成功', response, self.fileList);
                    loading.close();
                    param.onSuccess();
                    self.dialogAddFeedback.addForm.attachmentList.push({
                        fileName: self.fileList[self.fileList.length - 1].name,
                        fileUrl: response.data,
                    });
                })
                .catch(response => {
                    loading.close();
                    ElMessage({
                        message: '本次上传文件失败',
                        type: 'warning'
                    });
                    param.onError();
                });
        },
        onBeforeUploadImage(file) {
            console.log('onBeforeUploadImage', file);
            return true;
        },
        delFile(file, fileList) {
            let self = this;
            console.log('delFile', file, fileList);
            self.dialogAddFeedback.addForm.attachmentList = self.dialogAddFeedback.addForm.attachmentList.filter(item => item.fileName !== file.name);
            console.log('删除后', self.dialogAddFeedback.addForm.attachmentList);
            self.fileList = fileList;
            return true;
        },
        closeAdd() {
            console.log('关闭添加反馈');
            this.dialogEvaluate.show = false;
            this.dialogEvaluate.addForm = {
                satisfaction: 0,
                suggestion: '',
            };
            this.activeName = 'myTask';
            this.dialogAddFeedback.show = false;
            this.dialogAddFeedback.addForm = {
                customerName: '',
                contactName: '',
                attachmentList: [],
                title: '',
                source: 2,
                description: '',
                contactPhone: '',
                categoryId: '', // 反馈类型
                createdBy: userInfo.userId, // 创建人
            };
            this.fileList = [];
            this.$refs.userFormRef.resetFields();
        },
        submitAddFeedBack() {
            let self = this;
            // if (self.dialogAddFeedback.addForm.attachmentList.length != 0) {
            //     self.dialogAddFeedback.addForm.attachmentList = JSON.stringify(self.dialogAddFeedback.addForm.attachmentList);

            // }
            this.$refs.userFormRef.validate(valid => {
                if (valid) {
                    if (self.dialogAddFeedback.addForm.id) {
                        feedBack.updateFeedBack(self.dialogAddFeedback.addForm).then(res => {
                            ElMessage.success('修改成功');
                            self.closeAdd()
                            self.getData();

                        }).catch(error => {
                            ElMessage.error('修改失败');
                        });
                    } else {
                        self.dialogAddFeedback.addForm.currentHandlerId = 1;
                        feedBack.addFeedBack(self.dialogAddFeedback.addForm).then(res => {
                            ElMessage.success('添加成功');
                            // self.dialogAddFeedback.show = false;
                            self.closeAdd()
                            self.getData();
                        }).catch(error => {
                            ElMessage.error('添加失败');
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        /*跨多列*/
        arraySpanMethod(row) {
            if (row.rowIndex % 2 == 0) {
                if (row.columnIndex === 0) {
                    return [1, 8];
                }
            }
        },
        /*选择第几页*/
        handleCurrentChange(val) {
            let self = this;
            self.current = val;
            this.handleClick(self.activeName);
        },

        // /*每页多少条*/
        // handleSizeChange(val) {
        //     this.current = 1;
        //     this.pageSize = val;
        //     this.handleClick(self.activeName);
        // },

        /*切换菜单*/
        handleClick(tab) {
            console.log('切换菜单', tab);
            let self = this;
            // self.curPage = 1;
            self.activeName = tab;
            switch (tab) {
                case 'myTask':
                    self.getMyTaskData(userInfo.userId);
                    break;
                case 'all':
                    self.getData();
                    break;
                case 'awaitVisit':
                    self.getMyTaskData(-1);
                    break;
                default:
                    break;
            }
        },
        getMyTaskData(uid) {
            let self = this;
            let Params = {};
            Params.current = self.current;
            // Params.pageSize = self.pageSize;
            Params.uid = uid;
            self.loading = true;
            feedBack.feedBackList(Params, true).then(res => {
                console.log('获取我的任务列表', res);
                self.tableData = res.data;
                // self.totalDataNumber = res.data;
                self.loading = false;
            }).catch(error => {
                self.loading = false;
            });
        },
        /*获取列表*/
        getData() {
            let self = this;
            let Params = {};
            Params.current = self.current;
            // Params.pageSize = self.pageSize;
            self.loading = true;
            feedBack.workOrderList(Params, true).then(res => {
                self.tableData = res.data.records;
                self.totalDataNumber = res.data.total;
                self.loading = false;
            }).catch(error => {
                self.loading = false;
            });

        },
        updateClickDetail(row) {
            feedBack.workOrderDetail(row.id).then(res => {
                this.dialogAddFeedback.show = true;
                this.dialogAddFeedback.addForm = res.data;
                this.dialogAddFeedback.addForm.categoryId = res.data.categoryId ? res.data.categoryId * 1 : null;
                this.fileList = res.data.attachmentList.map(item => {
                    return {
                        name: item.fileName,
                        url: item.fileUrl
                    };
                });
            }).catch(error => {
                this.$message.error('获取详情失败');
            });
        },
        /*打开详情*/
        addClickDetail(row) {
            this.$router.push({
                path: '/feedback/detail',
                query: {
                    status:row.status,
                    cid: row.cid,
                    id: row.id,
                    pid:row.processInstanceId
                }
            });
        },
        // 新建咨询单
        addClick() {
            this.dialogAddFeedback.show = true;
            // this.$router.push('/consultation/consultation/add');
        },
        /*搜索查询*/
        onSubmit() {
            this.curPage = 1;
            this.tableData = [];
            switch (this.activeName) {
                case 'myTask':
                    this.getMyTaskData(userInfo.userId);
                    break;
                case 'all':
                    this.getData();
                    break;
                case 'awaitVisit':
                    this.getMyTaskData(-1);
                    break;
                default:
                    break;
            }
        },
        // 当前状态类型转换
        convertCurrentProcessType(currentProcessType) {
            if (currentProcessType === 1) {
                return "编辑问题";
            }
            if (currentProcessType === 2) {
                return "专家处理";
            }
            if (currentProcessType === 3) {
                return "竣工";
            }
            return currentProcessType;
        }
    }
};
</script>
<style lang="scss">
.product-info {
    padding: 10px 0;
    border-top: 1px solid #eeeeee;
}

.order-code .state-text {
    padding: 2px 4px;
    border-radius: 4px;
    background: #808080;
    color: #ffffff;
    margin-right: 6px;
}

.order-code .state-text-red {
    background: red;
}

.table-wrap .product-info:first-of-type {
    border-top: none;
}

.table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
    background: #f5f7fa;
}
</style>