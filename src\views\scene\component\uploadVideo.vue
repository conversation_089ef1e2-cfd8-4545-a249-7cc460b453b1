<template>
    
    <div class="uploadVideo">
        <div class="item" v-if="modelValue">
            <video :src="modelValue" controls></video>
            <span class="item-cut" @click="handleCutItem(index)">×</span>
        </div>
        <div class="item add" v-else @click="handleUploadOpen">
            <el-icon>
                <Plus />
            </el-icon>
        </div>
    </div>
    <Upload v-if="dialog" :config="{total: 1, fileType: 'video'}" :isupload="true" @returnImgs="handleUploadClose">上传图片</Upload>
</template>
<script setup>
import { ref, defineProps, defineEmits }   from 'vue'
const props = defineProps({
    modelValue: {
        type:String,
    }
})
const emit = defineEmits(['update:modelValue'])

const handleCutItem = (index) =>{
    emit('update:modelValue', "")
}
const dialog = ref(null)
const handleUploadOpen = () =>{
    dialog.value = true
}
const handleUploadClose = (e) =>{
    if(e && e[0]){
        emit('update:modelValue', e[0].filePath)
    }
    dialog.value = false
}
</script>

<style lang="scss" scoped>
.uploadVideo {
    display: flex;
    align-items: flex-start;
    .item {
        flex-shrink: 0;
        display: inline-flex;
        width: 180px;
        height: 100px;
        cursor: pointer;
        user-select: none;
        position: relative;
        .item-cut{
            position: absolute;
            top: -10px;
            right: -10px;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            font-size: 20px;
            color: #b2b2b2;
            border-radius: 50%;
            background: rgba($color: #000000, $alpha: 0.1);
            border: 1px solid #b2b2b2;
            transition: .2s;
        }
        &:hover{
            .item-cut{
                border-color: rgb(255, 107, 107);
                color: rgb(255, 107, 107);
                &:hover{
                    
                    background-color: red;
                    color: #fff;
                }
            }
        }
        &.add {
            border: 1px solid #dcdfe6;
            justify-content: center;
            align-items: center;
            border: 1px dashed #dddddd;
        }
    }
}
</style>