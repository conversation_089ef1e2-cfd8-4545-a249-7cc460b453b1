<!--我的咨询页面-->
<template>
    <div class="user">
        <!--搜索表单-->
        <div class="common-seach-wrap">
            <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="专家名称">
                    <el-input size="small" v-model="searchName" placeholder="请输入专家名称" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" icon="Search" @click="onSubmit">查询</el-button>
                </el-form-item>

                <el-form-item>
                    <el-button size="small" type="primary" icon="Plus" @click="addClick">新增专家</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!--内容-->
        <div class="product-content">
            <div class="table-wrap">
                <el-table size="small" :data="tableData" border style="width: 100%"  max-height="calc(100vh - 250px)" v-loading="loading">
                    <el-table-column prop="name" label="专家姓名"></el-table-column>
                    <el-table-column prop="phone" label="联系方式"></el-table-column>
                    <el-table-column prop="businessArea" label="负责领域"></el-table-column>
                    <!-- <el-table-column prop="createTime" label="创建时间"></el-table-column> -->
                    <el-table-column fixed="right" label="操作" width="130">
                        <template #default="scope">
                            <el-button @click="addClickDetail(scope.row)" link :type="'primary'"
                                size="small">修改</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog title="添加专家" v-model="dialogAddFeedback.show" width="400px" :destroy-on-close="true"
            :close-on-click-modal="false">
            <el-form :model="dialogAddFeedback.addForm" :rules="rulesUser" ref="userFormRef" label-width="120px"
                label-position="top">
                <el-form-item label="专家姓名" prop="nickname">
                    <el-input v-model="dialogAddFeedback.addForm.nickname" placeholder="请输入专家姓名" />
                </el-form-item>
                <el-form-item label="联系方式" prop="mobile">
                    <el-input v-model="dialogAddFeedback.addForm.mobile" placeholder="请输入手机号" />
                </el-form-item>
                <div style="display: flex;justify-content: center;">
                    <el-button type="primary" style="width: 150px;" @click="submitAddUser">添加</el-button>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
import FileApi from '@/api/file.js';
export default {
    components: {
    },
    data() {
        return {
            dialogAddFeedback: {
                show: false,
                addForm: {
                    nickname: '',
                    area: '',
                    mobile: '',
                }
            },
            token,
            /*是否加载完成*/
            loading: false,
            /*列表数据*/
            tableData: [],
            /*一页多少条*/
            pageSize: 10,
            /*一共多少条数据*/
            totalDataNumber: 0,
            /*当前是第几页*/
            curPage: 1,
            /*横向表单数据模型*/
            searchName: '',
            /*配送方式*/
            exStyle: [],
            order_no: ''
        };
    },
    created() {
        /*获取列表*/
        this.getData();
    },
    methods: {

        /*选择第几页*/
        handleCurrentChange(val) {
            let self = this;
            self.curPage = val;
            self.getData();
        },


        /*获取列表*/
        getData() {
            let self = this;
            feedBack.getBusinessListByName({ name: this.searchName }).then(res => {
                console.log('远程查询用户列表', res);
                self.tableData = res.data;
            }).catch(error => {
                ElMessage.error('获取用户列表失败');
            });
        },
        /*搜索查询*/
        onSubmit() {
            this.curPage = 1;
            this.tableData = [];
            this.getData();
        },
    }
};
</script>
<style lang="scss">
.product-info {
    padding: 10px 0;
    border-top: 1px solid #eeeeee;
}

.order-code .state-text {
    padding: 2px 4px;
    border-radius: 4px;
    background: #808080;
    color: #ffffff;
    margin-right: 6px;
}

.order-code .state-text-red {
    background: red;
}

.table-wrap .product-info:first-of-type {
    border-top: none;
}

.table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
    background: #f5f7fa;
}
</style>