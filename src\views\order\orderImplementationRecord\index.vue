<!--工单管理页面-->
<template>
  <div class="user">
    <!--搜索表单-->
    <div class="common-seach-wrap">
      <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="订单号"><el-input size="small" v-model="searchForm.orderNo"
            placeholder="请输入订单号"></el-input></el-form-item>
        <el-form-item label="配送方式">
          <el-select size="small" v-model="searchForm.style_id" placeholder="请选择" style="width: 150px">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="(item, index) in exStyle" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起始时间">
          <div class="block">
            <span class="demonstration"></span>
            <el-date-picker size="small" v-model="searchForm.createTime" type="daterange" value-format="YYYY-MM-DD"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item><el-button size="small" type="primary" icon="Search"
            @click="onSubmit">查询</el-button></el-form-item>
        <el-form-item><el-button size="small" type="success" @click="onExport"
            v-auth="'/order/operate/export'">导出</el-button></el-form-item>
      </el-form>
    </div>
    <!--内容-->
    <div class="product-content">
      <div class="table-wrap">
        <el-tabs v-model="activeName" @tab-change="handleClick">
          <!-- 其他类型的菜单，先注释，不要删除，后面可能有用
          <el-tab-pane label="全部订单" name="all"></el-tab-pane>
          <el-tab-pane :label="'未付款(' + order_count.payment + ')'" name="payment"></el-tab-pane>
          <el-tab-pane :label="'已付款，待开通(' + order_count.delivery + ')'" name="delivery"></el-tab-pane>
          <el-tab-pane :label="'开通中，待收货(' + order_count.received + ')'" name="received"></el-tab-pane>
          <el-tab-pane label="待评价" name="comment"></el-tab-pane>
          <el-tab-pane label="已完成" name="six"></el-tab-pane>
          <el-tab-pane label="已取消" name="cancel"></el-tab-pane>
          <el-tab-pane label="取消申请中" name="cancelApply"></el-tab-pane>
          -->
          <el-tab-pane :label="'待施工(' + order_count.waitingForImplementation +')'" name="waitingForImplementation"></el-tab-pane>
          <el-tab-pane label="已施工" name="implementationCompleted"></el-tab-pane>
        </el-tabs>
        <el-table size="small" :data="tableData.data" :span-method="arraySpanMethod" border style="width: 100%"
          v-loading="loading">
          <el-table-column label="订单信息" width="400">
            <template #default="scope">
              <div class="order-code" v-if="scope.row.isTopRow">
                <span class="state-text"
                  :class="{ 'state-text-red': scope.row.orderSource != 10 }">{{ scope.row.orderSourceText }}</span>
                <span class="c_main">订单号：{{ scope.row.orderNo }}</span>
                <span class="pl16">下单时间：{{ scope.row.createTime }}</span>
                <!--是否取消申请中-->
                <span class="pl16" v-if="scope.row.orderStatus == 21"><el-tag effect="dark"
                    size="small">取消申请中</el-tag></span>
              </div>
              <template v-else>
                <div class="product-info" v-for="(item, index) in scope.row.productList" :key="index">
                  <div class="pic"><img v-img-url="item.imagePath" alt="" /></div>
                  <div class="info">
                    <div class="name gray3 product-name">
                      <span>{{ item.productName }}</span>
                    </div>
                    <div class="gray9" v-if="item.productAttr">{{ item.productAttr }}</div>
                    <div class="orange" v-if="item.refund">{{ item.refund.type.text }}-{{ item.refund.status.text }}
                    </div>
                  </div>
                  <div class="d-c-c d-c">
                    <div class="orange">￥ {{ item.productPrice }}</div>
                    <div class="gray3">x{{ item.totalNum }}</div>
                  </div>
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="pay_price" label="实付款">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <div class="orange">{{ scope.row.payPrice }}</div>
                <p class="gray9">(含运费：{{ scope.row.expressPrice }})</p>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="" label="买家">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <div>{{ scope.row.nickname }}</div>
                 <div class="gray9">ID：({{ scope.row.userId }})</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="交易状态">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                {{ scope.row.orderStatusText }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="pay_type.text" label="支付方式">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <span class="gray9">{{ scope.row.payTypeText }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="delivery_type.text" label="配送方式">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <span class="green">{{ scope.row.deliveryTypeText }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="评价" width="60">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <span v-if="scope.row.isComment == 0">未评价</span>
                <span v-else="">已评价</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="130">
            <template #default="scope">
              <template v-if="!scope.row.isTopRow">
                <el-button @click="addClick(scope.row)" type="text" size="small"
                  v-auth="'/order/order/detail'">详情</el-button>
                <el-button
                  v-if="scope.row.payStatus == 20 && scope.row.deliveryStatus == 10 && scope.row.orderStatus != 20 && scope.row.orderStatus != 21  && scope.row.deliveryType == 10
                  && (!scope.row.assembleStatus || scope.row.assembleStatus == 20)"
                  @click="addClick(scope.row)" type="text" size="small" v-auth="'/order/order/delivery'">
                  发货
                </el-button>
                <el-button
                  v-if="scope.row.payStatus == 20 && scope.row.deliveryStatus == 10 && scope.row.orderStatus != 20 && scope.row.orderStatus != 21  && scope.row.deliveryType == 20 && (!scope.row.assembleStatus || scope.row.assembleStatus == 20)"
                  @click="addClick(scope.row)" type="text" size="small" v-auth="'/order/operate/extract'">
                  核销
                </el-button>
                <el-button
                  v-if="scope.row.payStatus == 20 && scope.row.deliveryStatus == 10 && scope.row.orderStatus != 20 && scope.row.orderStatus != 21 && scope.row.orderStatus != 10 && scope.row.deliveryType == 30"
                  @click="addClick(scope.row)" type="text" size="small" v-auth="'/order/order/delivery'">
                  发货
                </el-button>
                <el-button v-if="scope.row.orderStatus == 21" @click="addClick(scope.row)" type="text" size="small"
                  v-auth="'/order/operate/confirmCancel'">审核</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页-->
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
          :current-page="curPage" :page-size="pageSize" layout="total, prev, pager, next, jumper"
          :total="totalDataNumber"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
  import OrderImplementationRecordApi from "@/api/orderImplementationRecord.js";
  import qs from 'qs';
  import { useUserStore } from '@/store';
  const { token } = useUserStore();
  export default {
    components: {
    },
    data() {
      return {
        token,
        /*切换菜单的初始页名称*/
        activeName: 'waitingForImplementation',
        /*是否加载完成*/
        loading: true,
        /*列表数据*/
        tableData: [],
        /*一页多少条*/
        pageSize: 10,
        /*一共多少条数据*/
        totalDataNumber: 0,
        /*当前是第几页*/
        curPage: 1,
        /*横向表单数据模型*/
        searchForm: {
          orderNo: '',
          style_id: '',
          store_id: '',
          createTime: ''
        },
        /*配送方式*/
        exStyle: [],
        /*门店列表*/
        shopList: [],
        /*时间*/
        create_time: '',
        /*统计*/
        order_count: {
          payment: 0,
          delivery: 0,
          received: 0,
          waitingForImplementation: 0,
        },
        /*当前编辑的对象*/
        order_no: ''
      };
    },
    created() {
      /*获取列表*/
      this.getData();
    },
    methods: {
      /*跨多列*/
      arraySpanMethod(row) {
        if (row.rowIndex % 2 == 0) {
          if (row.columnIndex === 0) {
            return [1, 8];
          }
        }
      },
      /*选择第几页*/
      handleCurrentChange(val) {
        let self = this;
        self.curPage = val;
        self.getData();
      },

      /*每页多少条*/
      handleSizeChange(val) {
        this.curPage = 1;
        this.pageSize = val;
        this.getData();
      },

      /*切换菜单*/
      handleClick(tab) {
        let self = this;
        self.curPage = 1;
        self.activeName = tab;
        self.getData();
      },

      /*获取列表*/
      getData() {
        let self = this;
        let search = this.searchForm;
        let Params = {};
        Params.dataType = self.activeName;
        Params.pageIndex = self.curPage;
        Params.pageSize = self.pageSize;
        if(search.createTime && search.createTime.length>1){
          Params.startDate = search.createTime[0];
          Params.endDate = search.createTime[1];
        }
        Params.orderNo = search.orderNo;
        Params.deliveryType = search.style_id;
        self.loading = true;
        OrderImplementationRecordApi.orderlist(Params, true)
          .then(res => {
            let list = [];
            for (let i = 0; i < res.data.orderList.records.length; i++) {
              let item = res.data.orderList.records[i];
              let topitem = {
                orderNo: item.orderNo,
                createTime: item.createTime,
                orderSource: item.orderSource,
                orderSourceText: item.orderSourceText,
                orderStatus: item.orderStatus,
                isTopRow: true
              };
              list.push(topitem);
              list.push(item);
            }
            self.tableData.data = list;

            self.totalDataNumber = res.data.orderList.total;
            self.exStyle = res.data.deliveryList;
            self.order_count = res.data.orderCount;
            self.loading = false;
          })
          .catch(error => {});
      },
      /*打开添加*/
      addClick(row) {
        let self = this;
        let params = row.orderId;
        self.$router.push({
          path: '/order/orderImplementationRecord/detail',
          query: {
            orderId: params
          }
        });
      },
      /*搜索查询*/
      onSubmit() {
        this.curPage = 1;
        this.tableData = [];
        this.getData();
      },
      // 导出查询结果到excel文件
      onExport: function() {
        let self = this;
        let search = this.searchForm;
        let Params = {};
        Params.dataType = self.activeName;
        if(search.createTime && search.createTime.length>1){
          Params.startDate = search.createTime[0];
          Params.endDate = search.createTime[1];
        }
        Params.tokensupplier = self.token;
        //let baseUrl = window.location.protocol + '//' + window.location.host;
        let baseUrl = import.meta.env.VITE_BASIC_URL;
        window.location.href = baseUrl + '/api/supplier/order/operate/export?' + qs.stringify(Params);
      },
    }
  };
</script>
<style lang="scss">
  .product-info {
    padding: 10px 0;
    border-top: 1px solid #eeeeee;
  }

  .order-code .state-text {
    padding: 2px 4px;
    border-radius: 4px;
    background: #808080;
    color: #ffffff;
    margin-right: 6px;
  }

  .order-code .state-text-red {
    background: red;
  }

  .table-wrap .product-info:first-of-type {
    border-top: none;
  }

  .table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
    background: #f5f7fa;
  }
</style>