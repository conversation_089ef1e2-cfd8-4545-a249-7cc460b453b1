<template>
    <ul class="uploadImgs">
        <li class="item" v-for="(item,index) in modelValue">
            <el-image
                style="width: 100px; height: 100px"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="modelValue"
                :initial-index="index"
                fit="cover"
            />
            <span class="item-cut" @click="handleCutItem(index)">×</span>
        </li>
        <li class="add" @click="handleUploadOpen">
            <el-icon>
                <Plus />
            </el-icon>
        </li>
    </ul>
    <Upload v-if="dialog" :config="{total: 5, fileType: 'image'}" :isupload="true" @returnImgs="handleUploadClose">上传图片</Upload>
</template>
<script setup>
import { ref, defineProps, defineEmits }   from 'vue'
const props = defineProps({
    modelValue: {
        type:Array,
        default:()=>[]
    }
})
const emit = defineEmits(['update:modelValue'])

const handleCutItem = (index) =>{
    let oldValue = JSON.parse(JSON.stringify(props.modelValue))
    oldValue.splice(index,1)
    emit('update:modelValue', oldValue)
}
const dialog = ref(null)
const handleUploadOpen = () =>{
    dialog.value = true
}
const handleUploadClose = (e) =>{
    if(e && e[0]){
        let oldValue = JSON.parse(JSON.stringify(props.modelValue))
        let addValue = e.map(e=>e.filePath);
        emit('update:modelValue', [...oldValue, ...addValue])
    }
    dialog.value = false
}
</script>

<style lang="scss" scoped>
.uploadImgs {
    display: flex;
    align-items: flex-start;
    li {
        flex-shrink: 0;
        display: inline-flex;
        width: 100px;
        height: 100px;
        cursor: pointer;
        user-select: none;
        position: relative;
        .item-cut{
            position: absolute;
            top: -10px;
            right: -10px;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            font-size: 20px;
            color: #b2b2b2;
            border-radius: 50%;
            background: rgba($color: #000000, $alpha: 0.1);
            border: 1px solid #b2b2b2;
            transition: .2s;
        }
        &:hover{
            .item-cut{
                border-color: rgb(255, 107, 107);
                color: rgb(255, 107, 107);
                &:hover{
                    
                    background-color: red;
                    color: #fff;
                }
            }
        }
        &.add {
            border: 1px solid #dcdfe6;
            justify-content: center;
            align-items: center;
            border: 1px dashed #dddddd;
        }
        &+li{
            margin-left: 10px;
        }
    }
}
</style>