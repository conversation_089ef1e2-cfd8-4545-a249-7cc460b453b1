import request from '@/utils/request'

let SupplierApi = {
  /*供应商列表*/
  supplierList(data, errorback) {
    return request._postBody('/supplier/supplier/supplier/index', data, errorback);
  },
  /*添加供应商*/
  toaddSupplier(data, errorback) {
    return request._post('/supplier/supplier/supplier/toAdd', data, errorback);
  },
  /*添加供应商*/
  addSupplier(data, errorback) {
    return request._postBody('/supplier/supplier/supplier/add', data, errorback);
  },
  /*供应商编辑*/
  toEditSupplier(data, errorback) {
    return request._post('/supplier/supplier/supplier/toEdit', data, errorback);
  },
  /*供应商编辑*/
  editSupplier(data, errorback) {
    return request._postBody('/supplier/supplier/supplier/edit', data, errorback);
  },
  /*删除供应商*/
  deleteSupplier(data, errorback) {
    return request._post('/supplier/supplier/supplier/delete', data, errorback);
  },
  /*供应商待审核列表*/
  supplierPendList(data, errorback) {
    return request._postBody('/supplier/supplier/supplier/apply', data, errorback);
  },
}
export default SupplierApi;
