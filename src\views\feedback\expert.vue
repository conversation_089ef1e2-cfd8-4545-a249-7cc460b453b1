<template>
    <div class="user">
        <!--搜索表单-->
        <div class="common-seach-wrap">
            <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="标题">
                    <el-input size="small" v-model="searchForm.title" placeholder="请输入标题" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" icon="Search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <!-- <el-form-item> -->
                <!-- <el-button size="small" type="primary" icon="Plus" @click="addClick">添加反馈</el-button> -->
                <!-- </el-form-item> -->
            </el-form>
        </div>
        <!--内容-->
        <div class="product-content">
            <div class="table-wrap">
                <el-tabs v-model="activeName" @tab-change="handleClick">
                    <el-tab-pane label="我的任务" name="myTask"></el-tab-pane>
                    <!-- <el-tab-pane label="回访待领取" name="awaitVisit"></el-tab-pane> -->
                    <el-tab-pane label="已完成" name="finished"></el-tab-pane>
                    <!-- <el-tab-pane label="数据列表" name="all"></el-tab-pane> -->
                </el-tabs>
                <el-table size="small" :data="tableData" border style="width: 100%" v-loading="loading">
                    <el-table-column prop="title" label="标题"></el-table-column>
                    <el-table-column prop="customerName" label="客户名称"></el-table-column>
                    <el-table-column prop="categoryId" label="反馈类型">
                        <template #default="scope">
                            <span>
                                {{this.exStyle.find(item => item.categoryId == scope.row.categoryId)?.name || '未知类型'}}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="contactName" label="联系人姓名"></el-table-column>
                    <el-table-column prop="contactPhone" label="联系方式"></el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column fixed="right" label="操作" width="280">
                        <template #default="scope">
                            <el-button @click="addClickDetail(scope.row)" link :type="'primary'"
                                size="small">查看任务</el-button>
                            <el-button @click="changeTask(scope.row)" link :type="'primary'"
                                size="small">转交任务</el-button>
                            <el-button @click="rejectTask(scope.row)" link :type="'primary'"
                                size="small">驳回任务</el-button>
                            <el-button @click="examineTask(scope.row)" link :type="'primary'"
                                size="small">完成任务</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!--分页-->
            <div class="pagination">
                <el-pagination @current-change="handleCurrentChange" background :current-page="current"
                    :page-size="pageSize" layout="total, prev, pager, next, jumper"
                    :total="totalDataNumber"></el-pagination>
            </div>
        </div>
        <el-dialog title="任务转交" v-model="dialogTrans.show" width="400px" :destroy-on-close="true"
            :close-on-click-modal="false">
            <el-form :model="dialogTrans.addForm" ref="userFormRef" :rules="rules" label-width="auto"
                label-position="top">
                <el-form-item label="转交专家" prop="userId">
                    <el-select size="medium" filterable remote v-model="dialogTrans.addForm.userId" clearable
                        placeholder="请选择" :remote-method="remoteBusiness">
                        <el-option v-for="item in businessList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="转交说明" prop="reason">
                    <el-input v-model="dialogTrans.addForm.reason" :rows="4" type="textarea" placeholder="请填写转交说明" />
                </el-form-item>
                <div style="display: flex;justify-content: center;">
                    <el-button type="primary" style="width: 150px;" @click="submitAddFeedBack">转交</el-button>
                    <el-button plan style="width: 150px;" @click="closeAdd">取消</el-button>
                </div>
            </el-form>
        </el-dialog>

        <el-dialog title="完成任务" v-model="dialogExamineTask.show" width="400px" :destroy-on-close="true"
            :close-on-click-modal="false">
            <el-form :model="dialogExamineTask.addForm" ref="userFormRef" :rules="rules" label-width="auto"
                label-position="top">
                <el-form-item label="处理方案描述" prop="remark">
                    <el-input v-model="dialogExamineTask.addForm.remark" :rows="4" type="textarea"
                        placeholder="请填写反馈内容" />
                </el-form-item>
                <el-form-item label="上传附件">
                    <el-upload class="avatar-uploader" multiple ref="upload" :limit="3" v-model:file-list="fileList"
                        :before-remove="delFile" :before-upload="onBeforeUploadImage" :http-request="UploadImage">
                        <el-button size="small" icon="Upload" type="primary">点击上传</el-button>
                    </el-upload>
                </el-form-item>

                <div style="display: flex;justify-content: center;">
                    <el-button type="primary" style="width: 150px;" @click="submitEndTask">完成</el-button>
                    <el-button plan style="width: 150px;" @click="closeAdd">取消</el-button>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
import feedBack from "@/api/feedBack.js";
import { useUserStore } from '@/store';
const { token, userInfo } = useUserStore();
import FileApi from '@/api/file.js';
// import tacklingEnterprises from '@/api/tacklingEnterprises.js';
export default {
    components: {
    },
    data() {
        return {
            userInfo,
            rules: {
                userId: [
                    {
                        required: true,
                        message: '请选择转交专家',
                        trigger: 'change',
                    },
                ],
                reason: [
                    { required: true, message: '请填写转交说明', trigger: 'blur' },
                ],
            },
            userList: [],
            businessList: [],
            dialogTrans: {
                show: false,
                addForm: {
                    cid: '',
                    // pid: '',
                    reason: '',
                    userId: '',
                }
            },
            dialogExamineTask: {
                show: false,
                addForm: {
                    cid: '',
                    pid: '',
                    remark: '',
                    remark1: '',
                }
            },
            token,
            fileList: [],
            /*切换菜单的初始页名称*/
            activeName: 'myTask',
            /*是否加载完成*/
            loading: false,
            /*列表数据*/
            tableData: [],
            /*一页多少条*/
            // pageSize: 10,
            /*一共多少条数据*/
            totalDataNumber: 0,
            /*当前是第几页*/
            current: 1,
            /*横向表单数据模型*/
            searchForm: {
                title: '',
            },
            /*反馈类型*/
            exStyle: [],
        };
    },
    created() {
        /*获取列表*/
        this.getType();
        // this.remoteMethod('')
        this.remoteBusiness('');
        this.getMyTaskData(userInfo.userId);
        // this.getData();

    },
    methods: {
        rejectTask(row) {
            ElMessageBox.prompt('请输入驳回原因', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(({ value }) => {
                    feedBack.rejectTaskInfo({ cid: row.cid, pid: row.processInstanceId, reason: value }).then(res => {
                        ElMessage.success('任务已驳回');
                        this.getMyTaskData(userInfo.userId);
                    }).catch(error => {
                        ElMessage.error('任务驳回失败');
                    });
                })
                .catch(() => {
                    ElMessage({
                        type: 'info',
                        message: '已取消',
                    })
                })
        },
        submitEndTask() {
            this.dialogExamineTask.addForm.remark1 = this.fileList.map(item => item.fileUrl).join(',');
            feedBack.examineTaskInfoPost(this.dialogExamineTask.addForm).then(res => {
                ElMessage.success('任务已通过');
                this.closeAdd();
                this.getMyTaskData(userInfo.userId);
            }).catch(error => {
                ElMessage.error('任务通过失败');
            });
        },
        examineTask(row) {
            this.dialogExamineTask.show = true;
            this.dialogExamineTask.addForm = row;
            // ElMessageBox.confirm(
            //     '是否确认通过该任务?',
            //     '提示',
            //     {
            //         confirmButtonText: '通过',
            //         cancelButtonText: '取消',
            //         type: 'warning',
            //     }
            // ).then((res) => {


            // }).catch((err) => {
            //     // console.log('通过任务', err);
            //     ElMessage({
            //         type: 'info',
            //         message: '已取消',
            //     });
            // });
        },
        changeTask(row) {
            this.dialogTrans.show = true;
            this.dialogTrans.addForm = {
                cid: row.cid,
                // pid: row.processInstanceId,
                reason: '',
                userId: row.currentHandlerId,
            };
        },
        getType() {
            let self = this;
            feedBack.feedbackTypeList().then(res => {
                console.log('获取类型', res);
                self.exStyle = res.data;
            }).catch(error => {
                ElMessage.error('获取类型失败');
            });
            // tacklingEnterprises.getUserList().then(res => {
            //     console.log('获取用户列表', res);
            //     self.userList = res.data;
            // }).catch(error => {
            //    ElMessage.error('获取用户列表失败');
            // });
        },
        remoteMethod(query) {
            let self = this;
            feedBack.getUserListByName({ name: query }).then(res => {
                console.log('远程查询用户列表', res);
                self.userList = res.data;
            }).catch(error => {
                ElMessage.error('获取用户列表失败');
            });
        },
        remoteBusiness(query) {
            let self = this;
            feedBack.getBusinessListByName({ name: query }).then(res => {
                // console.log('远程查询用户列表', res);
                self.businessList = res.data;
            }).catch(error => {
                ElMessage.error('获取专家列表失败');
            });
        },
        setCustomerName(value) {
            // console.log('设置客户名称', value, this.userList.find(item => item.nickname === value));
            this.dialogTrans.addForm.createdBy = this.userList.find(item => item.nickname === value).userId;
            // this.dialogTrans.addForm.customerName = value;
        },

        closeAdd() {
            console.log('关闭添加反馈');
            this.dialogExamineTask.show = false;
            this.dialogExamineTask.addForm = {
                cid: '',
                pid: '',
                remark: '',
                remark1: '',
            };
            this.dialogTrans.show = false;
            this.dialogTrans.addForm = {
                reason: '',
            };
            this.$refs.userFormRef.resetFields();
        },
        submitAddFeedBack() {
            let self = this;
            this.$refs.userFormRef.validate(valid => {
                if (valid) {
                    feedBack.transferTask(self.dialogTrans.addForm).then(res => {
                        ElMessage.success('任务转交成功');
                        self.dialogTrans.show = false;
                        self.dialogTrans.addForm = {
                            reason: '',
                        };
                        self.$refs.userFormRef.resetFields();
                        self.getMyTaskData(userInfo.userId);
                    }).catch(error => {
                        ElMessage.error('任务转交失败');
                    });
                } else {
                    return false;
                }
            });
        },

        /*选择第几页*/
        handleCurrentChange(val) {
            let self = this;
            self.current = val;
            this.handleClick(self.activeName);
        },
        /*切换菜单*/
        handleClick(tab) {
            console.log('切换菜单', tab);
            let self = this;
            // self.curPage = 1;
            self.activeName = tab;
            switch (tab) {
                case 'myTask':
                    self.getMyTaskData(userInfo.userId);
                    break;
                case 'awaitVisit':
                    self.getMyTaskData(-1);
                    break;
                default:
                    break;
            }
        },
        getMyTaskData(uid) {
            let self = this;
            let Params = {};
            Params.current = self.current;
            // Params.pageSize = self.pageSize;
            Params.uid = uid;
            self.loading = true;
            feedBack.feedBackList(Params, true).then(res => {
                self.tableData = res.data;
                // self.totalDataNumber = res.data.total;
                self.loading = false;
            }).catch(error => {
                self.loading = false;
            });
        },
        /*获取列表*/
        // getData() {
        //     let self = this;
        //     let Params = {};
        //     Params.current = self.current;
        //     // Params.pageSize = self.pageSize;
        //     self.loading = true;
        //     feedBack.workOrderList(Params, true).then(res => {
        //         self.tableData = res.data.records;
        //         self.totalDataNumber = res.data.total;
        //         self.loading = false;
        //     }).catch(error => {
        //         self.loading = false;
        //     });

        // },
        updateClickDetail(row) {
            feedBack.workOrderDetail(row.id).then(res => {
                this.dialogTrans.show = true;
                this.dialogTrans.addForm = res.data;
                this.dialogTrans.addForm.categoryId = res.data.categoryId ? res.data.categoryId * 1 : null;
                this.fileList = res.data.attachmentList.map(item => {
                    return {
                        name: item.fileName,
                        url: item.fileUrl
                    };
                });
            }).catch(error => {
                this.$message.error('获取详情失败');
            });
        },
        /*打开详情*/
        addClickDetail(row) {
            this.$router.push({
                path: '/feedback/detail',
                query: {
                    status: row.status,
                    cid: row.cid,
                    id: row.id,
                    pid: row.processInstanceId
                }
            });
        },
        /*搜索查询*/
        onSubmit() {
            this.curPage = 1;
            this.tableData = [];
            this.getMyTaskData(userInfo.userId);
            // this.getData();
        },

        UploadImage(param) {
            let self = this;
            const loading = ElLoading.service({
                lock: true,
                text: '文件上传中,请等待',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            const formData = new FormData();
            formData.append('iFile', param.file);
            formData.append("groupId", 0);
            formData.append("fileType", "feedback");
            FileApi.uploadFile(formData)
                .then(response => {
                    console.log('上传成功', response, self.fileList);
                    loading.close();
                    param.onSuccess();
                    self.fileList[self.fileList.length - 1].fileUrl = response.data;
                })
                .catch(response => {
                    loading.close();
                    ElMessage({
                        message: '本次上传文件失败',
                        type: 'warning'
                    });
                    param.onError();
                });
        },
        onBeforeUploadImage(file) {
            console.log('onBeforeUploadImage', file);
            return true;
        },
        delFile(file, fileList) {
            let self = this;
            // console.log('delFile', file, fileList);
            // self.dialogAddFeedback.addForm.attachmentList = self.dialogAddFeedback.addForm.attachmentList.filter(item => item.fileName !== file.name);
            // console.log('删除后', self.dialogAddFeedback.addForm.attachmentList);
            self.fileList = fileList;
            return true;
        },
    }
};
</script>
<style lang="scss">
.product-info {
    padding: 10px 0;
    border-top: 1px solid #eeeeee;
}

.order-code .state-text {
    padding: 2px 4px;
    border-radius: 4px;
    background: #808080;
    color: #ffffff;
    margin-right: 6px;
}

.order-code .state-text-red {
    background: red;
}

.table-wrap .product-info:first-of-type {
    border-top: none;
}

.table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
    background: #f5f7fa;
}
</style>