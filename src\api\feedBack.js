import request from "@/utils/request";

let feedBack = {
  /*工单列表*/
  workOrderList(data, errorback) {
    return request._get("/supplier/work/order/list", data, errorback);
  },
  feedBackList(data, errorback) {
    return request._get("/supplier/work/order/task/list", data, errorback);
  },
  // 根据ID查询工单详情
  workOrderDetail(id, errorback) {
    return request._get("/supplier/work/order/" + id, errorback);
  },
  // 创建新的工单记录
  addFeedBack(data, errorback) {
    return request._postBody("/supplier/work/order", data, errorback);
  },
  // 更新工单记录
  updateFeedBack(data, errorback) {
    return request._postBody("/supplier/work/order/update", data, errorback);
  },
  // 反馈类型
  feedbackTypeList(data, errorback) {
    return request._get(
      "/supplier/work/order/product/category",
      data,
      errorback
    );
  },
  getUserListByName(data, errorback) {
    return request._get("/supplier/work/order/user/list", data, errorback);
  },

  getBusinessListByName(data, errorback) {
    return request._get("/supplier/work/order/business/list", data, errorback);
  },
  // 转交任务
  transferTask(data, errorback) {
    return request._get("/supplier/work/order/transfer", data, errorback);
  },
  // 驳回任务
  rejectTaskInfo(data, errorback) {
    return request._get("/supplier/work/order/reject", data, errorback);
  },
  // 通过任务
  examineTaskInfo(data, errorback) {
    return request._get("/supplier/work/order/approve", data, errorback);
  },

  examineTaskInfoPost(data, errorback) {
    return request._postBody(
      `/supplier/work/order/approve?id=${data.id}&cid=${data.cid}&pid=${data.processInstanceId}`,
      data,
      errorback
    );
  },

  taskProgress(data, errorback) {
    return request._get("/supplier/work/order/progress", data, errorback);
  },
  taskHistory(data, errorback) {
    return request._get("/supplier/work/order/history", data, errorback);
  },
};

export default feedBack;
