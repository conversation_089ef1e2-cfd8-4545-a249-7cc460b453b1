<!--企业管理页面-->
<template>
  <div class="user">
    <!--搜索表单-->
    <div class="common-seach-wrap">
      <el-row>
        <el-col>
          <el-form size="small" :inline="true" :model="searchForm" class="demo-form-inline">
            <el-form-item label="认领人">
              <el-input size="small" clearable v-model="searchForm.userName" placeholder="认领人" />
            </el-form-item>
            <el-form-item label="月份">
              <el-date-picker v-model="searchForm.claimMonth" style="width: 180px;" value-format="YYYY-MM" type="month"
                placeholder="认领月份">
              </el-date-picker> </el-form-item>
            <el-form-item label="区县">
              <el-select style="width: 180px;" size="medium" v-model="searchForm.area" clearable placeholder="请选择">
                <el-option v-for="item in ['柯桥', '绍兴分公司(本部)', '越城', '诸暨', '新昌', '嵊州', '上虞']" :key="item" :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="认领企业">
              <el-input size="small" clearable v-model="searchForm.enterpriseName" placeholder="认领企业" />
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" icon="Search" @click="onSearch">查询</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" @click="resetSearch">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="lookEnterprise">攻坚企业</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="addUser">新增用户</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="success" @click="importOpen">导入</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="handleBind">绑定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col>
          <div class="summary-bar">
            <div class="summary-item">
              <span class="summary-label">总数：</span>
              <span class="summary-value main">{{ sumData.sum }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">助力总数：</span>
              <span class="summary-value">{{ sumData.zlsum }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">助力成功数：</span>
              <span class="summary-value">{{ sumData.cgsum }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">拜访总数：</span>
              <span class="summary-value">{{ sumData.bfsum }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">金额：</span>
              <span class="summary-value">{{ sumData.jesum }}</span>
            </div>
          </div>
        </el-col>

      </el-row>


    </div>
    <!--内容-->
    <div class="product-content">
      <div class="table-wrap">
        <el-table size="small" :data="table.data" border style="width: 100%" v-loading="table.loading">
          <el-table-column prop="id" label="id" width="50px" align="center" />
          <el-table-column prop="enterpriseName" label="企业名称" width="250px" />
          <el-table-column prop="status" label="认领状态" width="100px" align="center">
            <template #default="scope">
              <span>{{
                getOptionField(options.status, scope.row.status)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="claimMonth" label="认领月份" align="center" width="150px" />
          <el-table-column prop="area" label="区县" align="center" />
          <el-table-column prop="userName" label="认领人" align="center" />
          <el-table-column prop="helpStatus" label="企业助力状态" align="center">
            <template #default="scope">
              <span>{{
                getOptionField(options.helpStatus, scope.row.helpStatus)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="helpAmount" label="助力金额（元）" align="center" />
          <!-- <el-table-column
            prop="hasLtoRecord"
            label="是否已在LTO系统录入"
            align="center"
          >
            <template #default="scope">
              <span>{{
                getOptionField(options.hasLtoRecord, scope.row.hasLtoRecord)
              }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="createTime" label="上传时间" align="center" />

          <el-table-column fixed="right" label="操作" width="130" align="center">
            <template #default="scope">
              <el-button @click="handleDialog(scope.row)" link :type="'primary'" size="small">查看</el-button>
              <el-button @click="unBind(scope.row)" link :type="'primary'" size="small">解绑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页-->
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
          :current-page="table.pageNum" :page-size="table.pageSize" layout="total, prev, pager, next, jumper"
          :total="table.total"></el-pagination>
      </div>
    </div>
    <!-- 弹出框 - 详情 -->
    <el-dialog title="详情" v-model="dialogDetail.show" width="900px" append-to-body :destroy-on-close="true"
      :close-on-click-modal="false">
      <el-descriptions :column="2" border :label-width="170" v-loading="dialogDetail.loading">
        <el-descriptions-item label="认领月份">{{ dialogDetail.info.claimMonth }}</el-descriptions-item>
        <el-descriptions-item label="认领状态">{{ getOptionField(options.status, dialogDetail.info.status)
        }}</el-descriptions-item>
        <el-descriptions-item label="企业助力状态">{{ getOptionField(options.helpStatus, dialogDetail.info.helpStatus)
        }}</el-descriptions-item>
        <el-descriptions-item label="助力金额（元）">{{ dialogDetail.info.helpAmount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ dialogDetail.info.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ dialogDetail.info.updateTime }}</el-descriptions-item>
        <!-- <el-descriptions-item label="是否已在LTO系统录入" :span="2">{{ getOptionField(options.hasLtoRecord, dialogDetail.info.hasLtoRecord) }}</el-descriptions-item> -->
        <!-- <el-descriptions-item label="备注1" :span="2">{{ dialogDetail.info.remark1 }}</el-descriptions-item>
        <el-descriptions-item label="备注1" :span="2">{{ dialogDetail.info.remark2 }}</el-descriptions-item>
        <el-descriptions-item label="备注3" :span="2">{{ dialogDetail.info.remark3 }}</el-descriptions-item> -->
        <el-descriptions-item label="拜访记录" :span="2">
          <el-table size="small" :data="dialogDetail.info.enterpriseVisitLogs || []" border>
            <el-table-column prop="visitDate" label="拜访日期" width="120px" align="center"
              :formatter="(row) => row.visitDate.split(' ')[0]" />
            <!-- <el-table-column prop="visitStatus" label="拜访状态"  width="100px" align="center">
              <template #default="scope">
              <span>{{
                getOptionField(options.visitStatus, scope.row.visitStatus)
              }}</span>
            </template>
            </el-table-column> -->
            <el-table-column prop="visitRemark" label="拜访备注" />
            <el-table-column prop="visitImages" label="拜访图片">
              <template #default="scope">
                <img-preview :imgList="scope.row.visitImages" />
              </template>
            </el-table-column>

          </el-table>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <el-dialog title="添加用户" v-model="dialogAddUser.show" width="400px" :destroy-on-close="true"
      :close-on-click-modal="false">
      <el-form :model="dialogAddUser.userForm" :rules="rulesUser" ref="userFormRef" label-width="120px"
        label-position="top">
        <el-form-item label="用户名称" prop="nickname">
          <el-input v-model="dialogAddUser.userForm.nickname" placeholder="请输入用户名称" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="dialogAddUser.userForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="区县" prop="area">
          <el-select size="medium" v-model="dialogAddUser.userForm.area" clearable placeholder="请选择">
            <el-option v-for="item in ['柯桥', '绍兴分公司(本部)', '越城', '诸暨', '新昌', '嵊州', '上虞']" :key="item" :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex;justify-content: center;">
          <el-button type="primary" style="width: 150px;" @click="submitAddUser">添加</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="百日攻坚企业管理" v-model="dialogLookEnterprise.show" width="1100px" :destroy-on-close="true"
      :close-on-click-modal="false">
      <el-form size="small" :inline="true" :model="dialogLookEnterprise.tableForm" class="demo-form-inline">
        <el-form-item label="客户名称">
          <el-input size="small" clearable v-model="dialogLookEnterprise.tableForm.customerName" placeholder="客户名称" />
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Search" @click="lookEnterprise">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" @click="resetEnterprise">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="addEnterprise">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="dialogLookEnterprise.tabList" border style="width: 100%">
        <el-table-column prop="customerName" label="客户名称">
        </el-table-column>
        <el-table-column prop="area" label="区县" align="center" width="150px">
        </el-table-column>
        <el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码">
        </el-table-column>
        <el-table-column prop="groupPcode" label="集团P码">
        </el-table-column>
        <el-table-column label="操作" align="center" width="120px">
          <template #default="scope">
            <el-button type="primary" link size="mini" @click="editEnter(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination @size-change="handleSizeEnterTab" @current-change="handleCurrentEnterTab" background
          :current-page="dialogLookEnterprise.pageNum" :page-size="dialogLookEnterprise.pageSize"
          layout="total, prev, pager, next, jumper" :total="dialogLookEnterprise.total"></el-pagination>
      </div>
      <el-dialog width="400px" :title="dialogLookEnterprise.addEnterForm?.id ? '修改百日攻坚企业' : '添加百日攻坚企业'"
        v-model="dialogLookEnterprise.addEnterpriseShow" append-to-body>
        <el-form :model="dialogLookEnterprise.addEnterForm" :rules="rules" ref="formRef" label-width="120px"
          label-position="top">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="dialogLookEnterprise.addEnterForm.customerName" placeholder="请输入客户名称" />
          </el-form-item>
          <el-form-item label="区县" prop="area">
            <el-select size="medium" v-model="dialogLookEnterprise.addEnterForm.area" clearable placeholder="请选择">
              <el-option v-for="item in ['柯桥', '绍兴分公司(本部)', '越城', '诸暨', '新昌', '嵊州', '上虞']" :key="item" :label="item"
                :value="item">
              </el-option>
            </el-select>
            <!-- <el-input v-model="dialogLookEnterprise.addEnterForm.area" placeholder="请输入区县" /> -->
          </el-form-item>
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="dialogLookEnterprise.addEnterForm.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
          </el-form-item>
          <el-form-item label="集团P码" prop="groupPcode">
            <el-input v-model="dialogLookEnterprise.addEnterForm.groupPcode" placeholder="请输入集团P码" />
          </el-form-item>
          <div style="display: flex;justify-content: center;">
            <el-button type="primary" style="width: 150px;" @click="submitAddEnterprise">{{
              dialogLookEnterprise.addEnterForm?.id ? '修改' : '添加' }}</el-button>
          </div>
        </el-form>
      </el-dialog>

    </el-dialog>
    <el-dialog title="导入" v-model="importInfo.show" width="500px" :destroy-on-close="true"
      :close-on-click-modal="false">
      <el-upload ref="upload" :limit="1" accept=".xlsx" :headers="importInfo.headers" :action="importInfo.url"
        v-loading="importInfo.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <el-icon style="font-size: 50px;">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip" @click.stop>
          <span>仅允许导入xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板
          </el-link>
        </div>

      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="importInfo.show = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="绑定" v-model="bindInfo.show" width="400px" :destroy-on-close="true" :close-on-click-modal="false">
      <el-form :model="bindInfo.bindForm" label-width="120px" label-position="top">
        <el-form-item label="绑定用户">
          <el-select v-model="bindInfo.bindForm.userId" filterable remote reserve-keyword placeholder="请搜索要绑定的用户"
            remote-show-suffix :remote-method="getUser">
            <el-option v-for="item in bindInfo.userList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择企业">
          <el-select v-model="bindInfo.bindForm.enterpriseId" filterable remote reserve-keyword placeholder="请搜索要绑定的企业"
            remote-show-suffix :remote-method="getEnterprise">
            <el-option v-for="item in bindInfo.enterpriseList" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="认领月份">
          <el-date-picker v-model="bindInfo.bindForm.claimMonth" style="width: 100%;" value-format="YYYY-MM"
            type="month" placeholder="认领月份"></el-date-picker>
        </el-form-item>
        <div style="display: flex;justify-content: center;">
          <el-button type="primary" style="width: 150px;" @click="onBind">绑定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import leaderManagementApi from "@/api/leaderManagement.js";
import tacklingEnterprisesApi from "@/api/tacklingEnterprises.js";
import { useUserStore } from "@/store";
import configObj from "@/config";
let { baseURL, tokenName, contentType, withCredentials, responseType } = configObj;
import { ElMessage, ElMessageBox } from "element-plus";
const { token } = useUserStore();
import imgPreview from './imgPreview.vue'
export default {
  components: {
    imgPreview
  },
  data() {
    return {
      token,
      sumData: {},
      bindInfo: {
        show: false,
        userList: [],
        enterpriseList: [],
        bindForm: {
          userId: '',
          enterpriseId: '',
          claimMonth: '',
        }
      },
      importInfo: {
        show: false,
        isUploading: false,
        headers: {
          [tokenName]: token
        },
        url: baseURL + '/supplier/enterprise/claim/addex',
      },
      dialogLookEnterprise: {
        addEnterpriseShow: false,
        addEnterForm: {
          customerName: '',
          area: '',
          unifiedSocialCreditCode: '',
          groupPcode: '',
        },
        show: false,
        tabList: [],
        total: 0,
        tableForm: {
          current: 1,
          pageSize: 10,
          customerName: '',
        }
      },
      dialogAddUser: {
        show: false,
        userForm: {
          nickname: '',
          area: '',
          mobile: '',
        }
      },
      searchForm: {
        userName: '',
        claimMonth: '',
        enterpriseName: '',
        area: ''
      },

      table: {
        data: [],
        total: 0,
        loading: false,
        pageSize: 10,
        pageNum: 1,
      },

      dialogDetail: {
        show: false,
        loading: false,
        info: {
          claimMonth: null,
          helpStatus: null,
          helpAmount: null,
          visitCount: null,
          hasLtoRecord: null,
          status: null,
          resetFlag: null,
          remark1: null,
          remark2: null,
          remark3: null,
          createTime: null,
          updateTime: null,
          enterpriseName: null,
          userName: null,
          enterpriseVisitLogs: null,
        },
      },
      options: {
        helpStatus: [
          { value: "-1", label: "失败" },
          { value: "0", label: "助力中" },
          { value: "4", label: "成功" },
        ],
        hasLtoRecord: [
          { value: "0", label: "否" },
          { value: "1", label: "是" },
        ],
        status: [
          { value: "0", label: "进行中" },
          { value: "1", label: "完成" },
          { value: "2", label: "逾期" },
        ],
        visitStatus: [
          { value: "0", label: "未拜访" },
          { value: "1", label: "已拜访" },
          { value: "2", label: "待安排" },
        ],
      },
      rules: {
        customerName: [
          { required: true, message: '请输入客户名称', trigger: 'blur' },
        ],
        unifiedSocialCreditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
        ],
        groupPcode: [
          { required: true, message: '请输入集团P码', trigger: 'blur' },
        ],
        area: [
          { required: true, message: '请选择区县', trigger: 'change' }
        ],
      },
      rulesUser: {
        nickname: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        area: [
          { required: true, message: '请选择区县', trigger: 'change' }
        ],
      }
    };
  },
  created() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    this.searchForm.claimMonth = `${year}-${month}`
    /*获取列表*/
    this.getData();

  },

  methods: {
    getUser(query) {
      if (query) {
        tacklingEnterprisesApi.getUserList({ nickname: query }).then(res => {
          this.bindInfo.userList = res.data.map(item => {
            return {
              value: item.userId,
              label: item.nickname
            }
          })
        })

      }
      // else {
      // this.bindInfo.userList = []
      // }
    },
    getEnterprise(query) {
      if (query) {
        tacklingEnterprisesApi.getEnterpriseList({ name: query }).then(res => {
          this.bindInfo.enterpriseList = res.data.map(item => {
            return {
              value: item.id,
              label: item.customerName
            }
          })
        })

      }
      //  else {
      // this.bindInfo.enterpriseList = []
      // }
    },
    handleBind() {
      this.bindInfo.bindForm = {
        userId: '',
        enterpriseId: '',
        claimMonth: '',
      }
      this.bindInfo.show = true
    },
    onBind() {
      tacklingEnterprisesApi.addBind(this.bindInfo.bindForm).then(res => {
        this.bindInfo.show = false;
        ElMessage({
          message: '绑定成功',
          type: 'success',
        })
        this.getData();
      })
    },
    importOpen() {
      this.importInfo.show = true
    },
    handleFileUploadProgress(event, file, fileList) {
      this.importInfo.isUploading = true;
    },
    importTemplate() {
      const link = document.createElement('a');
      link.href = new URL('../../../assets/模板.xlsx', import.meta.url).href;
      link.download = '模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      console.log('%c [ response ]-435', 'font-size:13px; background:pink; color:#bf2c9f;', response)
      this.importInfo.isUploading = false;
      this.$refs.upload.clearFiles();
      if (response.errCode == 200) {
        this.getData();
        this.importInfo.show = false;
      }
      this.getData();
      ElMessage({
        message: response.msg,
        type: 'success',
      })
    },
    submitFileForm() {
      this.$refs.upload.submit();
    },
    addUser() {
      this.dialogAddUser.userForm = {
        nickname: '',
        area: '',
        mobile: '',
      }
      this.dialogAddUser.show = true;
    },
    submitAddUser() {
      this.$refs.userFormRef.validate((valid) => {
        if (valid) {
          tacklingEnterprisesApi.addUser(this.dialogAddUser.userForm).then(res => {
            this.dialogAddUser.show = false;
            ElMessage({
              message: '提交成功',
              type: 'success',
            })
          })
        } else {
          console.log('校验失败');
          return false;
        }
      })
    },
    addEnterprise() {
      this.dialogLookEnterprise.addEnterForm = {
        customerName: '',
        area: '',
        unifiedSocialCreditCode: '',
        groupPcode: '',
      }
      this.dialogLookEnterprise.addEnterpriseShow = true;
      // console.log('%c [ this.dialogLookEnterprise.addEnterpriseShow = true; ]-318', 'font-size:13px; background:pink; color:#bf2c9f;', this.dialogLookEnterprise.addEnterpriseShow)
    },
    editEnter(row) {
      this.dialogLookEnterprise.addEnterpriseShow = true;
      this.dialogLookEnterprise.addEnterForm = row;
    },
    submitAddEnterprise() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          tacklingEnterprisesApi.addEnterprise(this.dialogLookEnterprise.addEnterForm).then(res => {
            this.dialogLookEnterprise.addEnterpriseShow = false;
            this.lookEnterprise();
            ElMessage({
              message: '提交成功',
              type: 'success',
            })
          })
        } else {
          console.log('校验失败');
          return false;
        }
      })
    },
    unBind(row) {

      ElMessageBox.confirm(
        `是否确认解绑企业：${row.enterpriseName}`,
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          leaderManagementApi.jieChu({ id: row.id, deleted: 1 }).then(res => {
            ElMessage({
              type: 'success',
              message: res.msg,
            })
            this.getData()
          })

        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '取消解绑',
          })
        })

    },
    lookEnterprise() {
      // this.$refs.lookEnterpriseForm.resetFields();
      tacklingEnterprisesApi.getList({
        ...this.dialogLookEnterprise.tableForm,
      }).then(res => {
        this.dialogLookEnterprise.tabList = res.data.records;
        this.dialogLookEnterprise.total = res.data.total;
        this.dialogLookEnterprise.show = true;
      })
    },
    resetEnterprise() {
      this.dialogLookEnterprise.tableForm = {
        current: 1,
        pageSize: 10,
        customerName: '',
      }
      this.lookEnterprise();
    },
    handleSizeEnterTab(val) {
      this.dialogLookEnterprise.tableForm.current = 1;
      this.dialogLookEnterprise.tableForm.pageSize = val;
      this.lookEnterprise();
    },
    handleCurrentEnterTab(val) {
      this.dialogLookEnterprise.tableForm.current = val;
      this.lookEnterprise();
    },
    getOptionField(options, value) {
      let item = options.find((e) => e.value == value);
      return item && item.label || "-";
    },

    /*查询列表*/
    getData() {
      this.tableData = [];
      this.getSum()
      let Params = { ...this.searchForm };
      Params.current = this.table.pageNum;
      Params.pageSize = this.table.pageSize;
      // Params.userName = this.searchForm.userName;
      this.table.loading = true;
      leaderManagementApi
        .getList(Params, true)
        .then((res) => {

          this.table.data = res.data.records;
          this.table.total = res.data.total;
          this.table.loading = false;

        })
        .catch((error) => { });
    },
    getSum() {
      leaderManagementApi.listSum(this.searchForm).then(res => {
        this.sumData = res.data;
      })
    },
    handleCurrentChange(val) {
      this.table.pageNum = val;
      this.getData();
    },
    handleSizeChange(val) {
      this.table.pageNum = 1;
      this.table.pageSize = val;
      this.getData();
    },
    onSearch() {
      this.curPage = 1;
      this.getData();
    },
    resetSearch() {
      this.searchForm = {
        groupPcode: "",
        accountManager: "",
        branchOffice: "",
        userName: '',
        claimMonth: '',
        enterpriseName: '',
        area: ''
      };
      this.onSearch();
    },

    handleDialog(row) {
      this.dialogDetail.show = true;
      this.dialogDetail.loading = true;
      leaderManagementApi.getDetail({ id: row.id }, true).then((res) => {
        this.dialogDetail.info = res.data;
        this.dialogDetail.loading = false;
      });
    },
  },
};
</script>
<style lang="scss">
.df {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .el-select__wrapper {
  font-size: 12px !important;
}

.summary-bar {
  display: flex;
  // justify-content: flex-end;
  align-items: center;
  gap: 32px;
  padding: 12px 24px 12px 0;
  background: #fff;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.summary-item {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.summary-label {
  color: #888;
  font-size: 15px;
}

.summary-value {
  font-weight: bold;
  font-size: 22px;
  color: #333;
  transition: color 0.2s;
}

.summary-value.main {
  color: #409EFF; // Element Plus 主色
}

.product-info {
  padding: 10px 0;
  border-top: 1px solid #eeeeee;
}

.order-code .state-text {
  padding: 2px 4px;
  border-radius: 4px;
  background: #808080;
  color: #ffffff;
  margin-right: 6px;
}

.order-code .state-text-red {
  background: red;
}

.table-wrap .product-info:first-of-type {
  border-top: none;
}

.table-wrap .el-table__body tbody .el-table__row:nth-child(odd) {
  background: #f5f7fa;
}
</style>