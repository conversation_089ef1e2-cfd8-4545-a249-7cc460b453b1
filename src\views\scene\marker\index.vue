<template>
    <div>
        <div class="common-level-rail">
            <el-button size="small" type="primary" icon="Plus" @click="handleAdd">添加标记</el-button>
        </div>
        <div class="table-wrap">
            <el-table :data="table.data" style="width: 100%" v-loading="table.loading">
                <el-table-column prop="enterpriseMapName" label="标记名称"></el-table-column>
                <el-table-column prop="enterpriseMapLabel" label="标记提示"></el-table-column>
                <el-table-column prop="productName" label="关联产品"></el-table-column>
                <el-table-column prop="productName" label="操作" width="150" align="center">
                    <template #default="scope">
                        <el-button size="small" type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" link @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!--分页-->
            <!-- <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
                    :current-page="table.pagination.pageIndex" :page-size="table.pagination.pageSize"
                    layout="total, prev, pager, next, jumper" :total="table.pagination.total"></el-pagination>
            </div> -->
        </div>
    </div>
</template>

<script setup>
import sceneApi from '@/api/scene.js';
import { reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const handleAdd = () => {
    router.push('/scene/marker/add')
}
const handleEdit = (row) =>{
    router.push({
        path:'/scene/marker/edit',
        query:{ id:row.id }
    })
}
const handleDelete = ({id}) =>{
    ElMessageBox.confirm('确定删除此标记？', '提示', {
        type: 'warning'
    })
    .then(() => {
        sceneApi.delMarker({id}).then(() => {
            ElMessage({
                message: '删除成功',
                type: 'success'
            });
            onSearch();
        });
    });
}

const table = reactive({
    loading: false,
    data: [],
    pagination: {
        total: 0,
        pageIndex: 1,
        pageSize: 100
    }

})
const onSearch = () => {
    let p = {}//table.pagination;
    table.loading = true;
    sceneApi.markerList(p, true)
        .then(res => {
            table.loading = false;
            table.data = res.data;

        })
        .catch(error => {
            table.loading = false;
        });
}
/*选择第几页*/
const handleCurrentChange = (val) => {
    table.pagination.pageIndex = val;
    onSearch()
}
/*每页多少条*/
const handleSizeChange = (val) => {
    table.pagination.pageSize = val;
    onSearch()
}



onMounted(() => {
    onSearch()
})
</script>

<style></style>